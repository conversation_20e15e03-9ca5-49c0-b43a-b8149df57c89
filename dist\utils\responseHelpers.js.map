{"version": 3, "file": "responseHelpers.js", "sourceRoot": "", "sources": ["../../src/utils/responseHelpers.ts"], "names": [], "mappings": ";;;AAgBA,8CAEC;AAUD,sDAaC;AAYD,0DAiBC;AAWD,kDAuBC;AASD,0CAyBC;AAyED,8DAWC;AAWD,sDAgBC;AAkCD,oDAcC;AAzRD,SAAgB,iBAAiB;IAC/B,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;AAC5E,CAAC;AAUD,SAAgB,qBAAqB,CACnC,IAAO,EACP,OAAgB,EAChB,QAA8B;IAE9B,OAAO;QACL,OAAO,EAAE,IAAI;QACb,IAAI;QACJ,OAAO;QACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,iBAAiB,EAAE;QAC9B,QAAQ;KACT,CAAC;AACJ,CAAC;AAYD,SAAgB,uBAAuB,CACrC,IAAS,EACT,UAA0B,EAC1B,OAA6B,EAC7B,IAA+C,EAC/C,QAA8B;IAE9B,OAAO;QACL,OAAO,EAAE,IAAI;QACb,IAAI;QACJ,UAAU;QACV,OAAO;QACP,IAAI;QACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,iBAAiB,EAAE;QAC9B,QAAQ;KACT,CAAC;AACJ,CAAC;AAWD,SAAgB,mBAAmB,CACjC,KAAa,EACb,aAAqB,GAAG,EACxB,OAAa,EACb,WAAsC;IAEtC,OAAO;QACL,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,IAAI,UAAU,EAAE;YACtB,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC;YACtD,QAAQ,EAAE,gBAAgB,CAAC,UAAU,CAAC;YACtC,QAAQ,EAAE,gBAAgB,CAAC,UAAU,CAAC;YACtC,UAAU;YACV,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,iBAAiB,EAAE;YAC9B,WAAW;SACZ;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,iBAAiB,EAAE;KAC/B,CAAC;AACJ,CAAC;AASD,SAAgB,eAAe,CAC7B,KAAU,EACV,OAA2B;IAE3B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC/D,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAElC,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;IAC3D,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;IAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;IAE5C,OAAO;QACL,IAAI,EAAE,cAAc;QACpB,UAAU,EAAE;YACV,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO,EAAE,IAAI,GAAG,UAAU;YAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;YACjB,KAAK,EAAE,cAAc,CAAC,MAAM;YAC5B,MAAM;SACP;KACF,CAAC;AACJ,CAAC;AASD,SAAS,sBAAsB,CAAC,KAAa,EAAE,UAAkB;IAC/D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAGvC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC3D,OAAO,4CAA4C,CAAC;IACtD,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC9D,OAAO,gDAAgD,CAAC;IAC1D,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC3D,OAAO,qDAAqD,CAAC;IAC/D,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC5D,OAAO,iDAAiD,CAAC;IAC3D,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAC5D,OAAO,0EAA0E,CAAC;IACpF,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QACxE,OAAO,+EAA+E,CAAC;IACzF,CAAC;IAED,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;QACtB,OAAO,0DAA0D,CAAC;IACpE,CAAC;IAED,OAAO,iDAAiD,CAAC;AAC3D,CAAC;AAKD,SAAS,gBAAgB,CAAC,UAAkB;IAC1C,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,YAAY,CAAC;IAC5C,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,gBAAgB,CAAC;IAChD,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,eAAe,CAAC;IAC/C,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,WAAW,CAAC;IAC3C,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,UAAU,CAAC;IAC1C,IAAI,UAAU,KAAK,GAAG;QAAE,OAAO,YAAY,CAAC;IAC5C,IAAI,UAAU,IAAI,GAAG;QAAE,OAAO,UAAU,CAAC;IACzC,OAAO,SAAS,CAAC;AACnB,CAAC;AAKD,SAAS,gBAAgB,CAAC,UAAkB;IAC1C,IAAI,UAAU,IAAI,GAAG;QAAE,OAAO,MAAM,CAAC;IACrC,IAAI,UAAU,IAAI,GAAG;QAAE,OAAO,QAAQ,CAAC;IACvC,OAAO,KAAK,CAAC;AACf,CAAC;AAQD,SAAgB,yBAAyB,CAAC,OAA2B;IACnE,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAChC,CAAC;IAED,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QACpC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;QACtD,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM;KACvC,CAAC;AACJ,CAAC;AAWD,SAAgB,qBAAqB,CACnC,OAAgB,EAChB,IAAQ,EACR,KAAc,EACd,QAA8B;IAE9B,OAAO;QACL,OAAO;QACP,IAAI;QACJ,KAAK;QACL,QAAQ,EAAE;YACR,GAAG,QAAQ;YACX,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,iBAAiB,EAAE;SAC/B;KACF,CAAC;AACJ,CAAC;AAKD,MAAa,aAAa;IAGxB;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;IACrC,CAAC;IAKD,iBAAiB,CAAC,WAAgC,EAAE;QAClD,OAAO;YACL,GAAG,QAAQ;YACX,cAAc,EAAE,IAAI,CAAC,UAAU,EAAE;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;CACF;AAxBD,sCAwBC;AAKD,SAAgB,oBAAoB,CAClC,MAAgC,EAChC,MAA2B,EAC3B,UAAkB,OAAO;IAEzB,OAAO;QACL,MAAM;QACN,OAAO;QACP,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,MAAM;QACN,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;QACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;KACnD,CAAC;AACJ,CAAC"}