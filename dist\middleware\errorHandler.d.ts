import { Request, Response, NextFunction } from 'express';
import { BaseError, ErrorCategory, ErrorSeverity } from '../types';
export declare class AppError extends Error implements BaseError {
    readonly code: string;
    readonly userMessage: string;
    readonly category: ErrorCategory;
    readonly severity: ErrorSeverity;
    readonly statusCode: number;
    readonly isOperational: boolean;
    readonly details?: Record<string, any>;
    readonly timestamp: Date;
    readonly requestId?: string;
    constructor(message: string, statusCode?: number, isOperational?: boolean, options?: {
        code?: string;
        userMessage?: string;
        category?: ErrorCategory;
        severity?: ErrorSeverity;
        details?: Record<string, any>;
        requestId?: string;
    });
    private getUserFriendlyMessage;
    private getErrorCategory;
    private getErrorSeverity;
}
export declare const errorHandler: (error: Error, req: Request, res: Response, _next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validationErrorHandler: (errors: Array<{
    field: string;
    message: string;
    value?: any;
}>) => AppError;
export declare const rateLimitErrorHandler: (limit: number, windowMs: number, retryAfter?: number) => AppError;
//# sourceMappingURL=errorHandler.d.ts.map