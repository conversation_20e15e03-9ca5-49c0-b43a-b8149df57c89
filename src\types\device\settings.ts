/**
 * Device settings types and interfaces
 * Comprehensive configuration management for SMS Gateway devices
 */

// Limit period enum for message rate limiting
export enum LimitPeriod {
  Disabled = 'Disabled',
  PerMinute = 'PerMinute',
  PerHour = 'PerHour',
  PerDay = 'PerDay'
}

// SIM selection mode for multi-SIM devices
export enum SimSelectionMode {
  OSDefault = 'OSDefault',
  RoundRobin = 'RoundRobin',
  Random = 'Random'
}

// Log level enumeration
export enum LogLevel {
  Debug = 'debug',
  Info = 'info',
  Warn = 'warn',
  Error = 'error'
}

// Enhanced settings interfaces with comprehensive documentation

/**
 * Message processing settings
 */
export interface SettingsMessages {
  /** Rate limiting period */
  limitPeriod?: LimitPeriod;
  /** Maximum messages per limit period */
  limitValue?: number;
  /** Message log retention in days */
  logLifetimeDays?: number;
  /** Minimum send interval in seconds */
  sendIntervalMin?: number;
  /** Maximum send interval in seconds */
  sendIntervalMax?: number;
  /** SIM card selection strategy */
  simSelectionMode?: SimSelectionMode;
  /** Enable delivery reports */
  enableDeliveryReports?: boolean;
  /** Maximum retry attempts for failed messages */
  maxRetryAttempts?: number;
  /** Retry delay in seconds */
  retryDelaySeconds?: number;
  /** Message queue size limit */
  queueSizeLimit?: number;
}

/**
 * Webhook configuration settings
 */
export interface SettingsWebhooks {
  /** Require internet connection for webhooks */
  internetRequired?: boolean;
  /** Number of retry attempts for failed webhooks */
  retryCount?: number;
  /** Webhook signing key for security */
  signingKey?: string;
  /** Webhook timeout in seconds */
  timeoutSeconds?: number;
  /** Enable webhook callbacks */
  enabled?: boolean;
  /** Custom headers for webhook requests */
  customHeaders?: Record<string, string>;
}

/**
 * Gateway connection settings
 */
export interface SettingsGateway {
  /** Gateway instance name */
  name?: string;
  /** Cloud service URL */
  cloudUrl?: string;
  /** Private authentication token */
  privateToken?: string;
  /** Connection timeout in seconds */
  connectionTimeoutSeconds?: number;
  /** Enable automatic reconnection */
  autoReconnect?: boolean;
  /** Reconnection delay in seconds */
  reconnectDelaySeconds?: number;
  /** Heartbeat interval in seconds */
  heartbeatIntervalSeconds?: number;
}

/**
 * Encryption settings for secure communication
 */
export interface SettingsEncryption {
  /** Enable message encryption */
  enabled?: boolean;
  /** Encryption passphrase */
  passphrase?: string;
  /** Encryption algorithm */
  algorithm?: 'AES-256' | 'AES-128';
  /** Key rotation interval in days */
  keyRotationDays?: number;
}

/**
 * Logging configuration
 */
export interface SettingsLogs {
  /** Log time-to-live in seconds */
  ttl?: number;
  /** Log retention period in days */
  lifetimeDays?: number;
  /** Log level */
  level?: LogLevel;
  /** Enable file logging */
  enableFileLogging?: boolean;
  /** Maximum log file size in MB */
  maxFileSizeMB?: number;
  /** Number of log files to retain */
  maxFiles?: number;
}

/**
 * Health check and ping settings
 */
export interface SettingsPing {
  /** Enable health check pings */
  enabled?: boolean;
  /** Ping interval (deprecated, use intervalSeconds) */
  interval?: number;
  /** Ping interval in seconds */
  intervalSeconds?: number;
  /** Ping timeout in seconds */
  timeoutSeconds?: number;
  /** Enable ping failure alerts */
  enableFailureAlerts?: boolean;
  /** Maximum consecutive failures before alert */
  maxFailuresBeforeAlert?: number;
}

/**
 * Main device settings interface with comprehensive configuration
 */
export interface DeviceSettings {
  /** Message processing settings */
  messages?: SettingsMessages;
  /** Webhook configuration */
  webhooks?: SettingsWebhooks;
  /** Gateway connection settings */
  gateway?: SettingsGateway;
  /** Encryption settings */
  encryption?: SettingsEncryption;
  /** Logging configuration */
  logs?: SettingsLogs;
  /** Health check settings */
  ping?: SettingsPing;
  /** Settings version for migration support */
  version?: string;
  /** Last updated timestamp */
  lastUpdated?: Date;
  /** Custom metadata */
  metadata?: Record<string, any>;
}

// Settings update requests with validation support
export interface UpdateSettingsRequest extends DeviceSettings {
  /** Validate settings before applying */
  validate?: boolean;
}

export interface PatchSettingsRequest extends Partial<DeviceSettings> {
  /** Validate settings before applying */
  validate?: boolean;
}

// Settings validation types
export interface SettingsValidationError {
  /** Field path (e.g., 'messages.limitValue') */
  field: string;
  /** Error message */
  message: string;
  /** Current invalid value */
  value: any;
  /** Suggested correction */
  suggestion?: string;
}

export interface SettingsValidationResult {
  /** Whether settings are valid */
  valid: boolean;
  /** Validation errors */
  errors: SettingsValidationError[];
  /** Validation warnings */
  warnings: SettingsValidationError[];
}

// Settings response types optimized for UI
export interface DeviceSettingsResponse {
  /** Current settings */
  settings: DeviceSettings;
  /** Default values for reference */
  defaults: DeviceSettings;
  /** Available options for enum fields */
  options: {
    limitPeriods: LimitPeriod[];
    simSelectionModes: SimSelectionMode[];
    logLevels: LogLevel[];
    encryptionAlgorithms: string[];
  };
  /** Settings metadata */
  metadata: {
    version: string;
    lastUpdated: Date;
    isDefault: boolean;
    hasUnsavedChanges: boolean;
  };
}

// Settings export/import for backup and migration
export interface SettingsExport {
  /** Settings data */
  settings: DeviceSettings;
  /** Export metadata */
  metadata: {
    exportedAt: Date;
    version: string;
    deviceId: string;
    deviceName: string;
  };
}

export interface SettingsImport {
  /** Settings to import */
  settings: Partial<DeviceSettings>;
  /** Import options */
  options: {
    /** Merge with existing settings */
    merge: boolean;
    /** Validate before import */
    validate: boolean;
    /** Backup current settings */
    backup: boolean;
  };
}
