/**
 * Test setup and configuration
 * Provides common test utilities, mocks, and setup functions
 */

import { jest } from '@jest/globals';

/**
 * Global test setup
 */
beforeAll(() => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  
  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'info').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  // Restore console methods
  jest.restoreAllMocks();
});

/**
 * Test utilities and helpers
 */
export class TestUtils {
  /**
   * Create mock device data
   */
  static createMockDevice(id: string = 'test-device', overrides: any = {}) {
    return {
      id,
      name: `Test Device ${id}`,
      lastSeen: '2024-01-15T10:00:00.000Z',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:00:00.000Z',
      deletedAt: null,
      ...overrides
    };
  }

  /**
   * Create mock device list item
   */
  static createMockDeviceListItem(id: string = 'test-device', overrides: any = {}) {
    return {
      id,
      name: `Test Device ${id}`,
      status: 'online',
      lastSeen: '2024-01-15T10:00:00.000Z',
      isConnected: true,
      messagesSentToday: 45,
      successRate: 98.5,
      ...overrides
    };
  }

  /**
   * Create mock device details
   */
  static createMockDeviceDetails(id: string = 'test-device', overrides: any = {}) {
    return {
      ...this.createMockDevice(id),
      status: 'online',
      connection: {
        isConnected: true,
        lastSeen: new Date('2024-01-15T10:00:00.000Z')
      },
      capabilities: {
        canSendSms: true,
        canReceiveSms: true,
        supportsDeliveryReports: true,
        maxMessageLength: 160,
        simSlots: [0, 1],
        features: ['sms', 'delivery_reports']
      },
      stats: {
        messagesSent: 1250,
        messagesReceived: 89,
        messagesSentToday: 45,
        successRate: 98.5,
        avgResponseTime: 1200,
        lastActivity: new Date('2024-01-15T10:00:00.000Z')
      },
      recentActivity: [],
      performance: {
        messagesPerHour: 50,
        avgDeliveryTime: 30,
        errorRate: 1.5,
        uptime: 98.5,
        trend: 'stable' as const
      },
      ...overrides
    };
  }

  /**
   * Create mock device settings
   */
  static createMockDeviceSettings(overrides: any = {}) {
    return {
      messages: {
        limitPeriod: 'PerHour',
        limitValue: 100,
        enableDeliveryReports: true,
        maxRetryAttempts: 3,
        retryDelaySeconds: 30,
        queueSizeLimit: 1000
      },
      webhooks: {
        enabled: true,
        url: 'https://example.com/webhook',
        retryCount: 3,
        timeoutSeconds: 30,
        customHeaders: {}
      },
      gateway: {
        name: 'Test Gateway',
        autoReconnect: true,
        reconnectDelaySeconds: 60,
        heartbeatIntervalSeconds: 30
      },
      encryption: {
        enabled: false
      },
      logs: {
        level: 'info',
        lifetimeDays: 30
      },
      ping: {
        enabled: true,
        intervalSeconds: 60
      },
      ...overrides
    };
  }

  /**
   * Create mock API response
   */
  static createMockApiResponse<T>(data: T, overrides: any = {}) {
    return {
      success: true,
      data,
      timestamp: '2024-01-15T10:30:00.000Z',
      requestId: 'req_test_123',
      ...overrides
    };
  }

  /**
   * Create mock paginated response
   */
  static createMockPaginatedResponse<T>(
    data: T[], 
    page: number = 1, 
    limit: number = 20, 
    total?: number,
    overrides: any = {}
  ) {
    const actualTotal = total ?? data.length;
    const totalPages = Math.ceil(actualTotal / limit);

    return {
      success: true,
      data,
      pagination: {
        page,
        limit,
        total: actualTotal,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        count: data.length,
        offset: (page - 1) * limit
      },
      timestamp: '2024-01-15T10:30:00.000Z',
      requestId: 'req_test_123',
      ...overrides
    };
  }

  /**
   * Create mock operation result
   */
  static createMockOperationResult(success: boolean = true, overrides: any = {}) {
    return {
      success,
      ...(success ? {} : { error: 'Operation failed' }),
      metadata: {
        timestamp: '2024-01-15T10:30:00.000Z'
      },
      ...overrides
    };
  }

  /**
   * Wait for a specified amount of time
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate random string for testing
   */
  static randomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Generate random number within range
   */
  static randomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Create mock Express request object
   */
  static createMockRequest(overrides: any = {}) {
    return {
      params: {},
      query: {},
      body: {},
      headers: {},
      method: 'GET',
      url: '/test',
      ip: '127.0.0.1',
      get: jest.fn(),
      ...overrides
    };
  }

  /**
   * Create mock Express response object
   */
  static createMockResponse() {
    const res: any = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      on: jest.fn().mockReturnThis(),
      locals: {}
    };
    return res;
  }

  /**
   * Create mock Express next function
   */
  static createMockNext() {
    return jest.fn();
  }
}

/**
 * Custom Jest matchers for better assertions
 */
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidApiResponse(): R;
      toBeValidPaginatedResponse(): R;
      toBeValidOperationResult(): R;
    }
  }
}

// Add custom matchers
expect.extend({
  toBeValidApiResponse(received) {
    const pass = received && 
                 typeof received.success === 'boolean' &&
                 received.timestamp &&
                 received.requestId;

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to be a valid API response`,
        pass: true,
      };
    } else {
      return {
        message: () => `Expected ${JSON.stringify(received)} to be a valid API response with success, timestamp, and requestId`,
        pass: false,
      };
    }
  },

  toBeValidPaginatedResponse(received) {
    const pass = received && 
                 typeof received.success === 'boolean' &&
                 Array.isArray(received.data) &&
                 received.pagination &&
                 typeof received.pagination.page === 'number' &&
                 typeof received.pagination.limit === 'number' &&
                 typeof received.pagination.total === 'number';

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to be a valid paginated response`,
        pass: true,
      };
    } else {
      return {
        message: () => `Expected ${JSON.stringify(received)} to be a valid paginated response`,
        pass: false,
      };
    }
  },

  toBeValidOperationResult(received) {
    const pass = received && 
                 typeof received.success === 'boolean' &&
                 (received.success || received.error);

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to be a valid operation result`,
        pass: true,
      };
    } else {
      return {
        message: () => `Expected ${JSON.stringify(received)} to be a valid operation result`,
        pass: false,
      };
    }
  }
});

export default TestUtils;
