"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const deviceService_1 = __importDefault(require("../services/deviceService"));
const BaseController_1 = require("./BaseController");
class DeviceController extends BaseController_1.BaseController {
    constructor() {
        super(...arguments);
        this.getDevices = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            this.logRequest(req, 'getDevices');
            const pagination = this.getPaginationOptions(req);
            const filters = this.getFilters(req, [
                'name', 'status', 'active', 'lastSeenAfter', 'lastSeenBefore', 'isConnected'
            ]);
            const deviceFilter = {};
            if (filters['name'])
                deviceFilter.name = filters['name'];
            if (filters['status'])
                deviceFilter.status = filters['status'];
            if (filters['active'])
                deviceFilter.active = filters['active'] === 'true';
            if (filters['isConnected'])
                deviceFilter.isConnected = filters['isConnected'] === 'true';
            if (filters['lastSeenAfter'])
                deviceFilter.lastSeenAfter = this.parseDate(filters['lastSeenAfter'], 'lastSeenAfter');
            if (filters['lastSeenBefore'])
                deviceFilter.lastSeenBefore = this.parseDate(filters['lastSeenBefore'], 'lastSeenBefore');
            const result = await deviceService_1.default.getDevices(deviceFilter, pagination);
            this.sendPaginated(res, result.data || [], result.pagination.total, pagination, deviceFilter, pagination.sortBy ? { field: pagination.sortBy, order: pagination.sortOrder || 'desc' } : undefined, timer.addTimingMetadata({
                filtersApplied: Object.keys(deviceFilter).length,
                totalDevices: result.pagination.total
            }));
        });
        this.getDevice = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const { deviceId } = req.params;
            this.logRequest(req, 'getDevice', { deviceId });
            this.validateRequiredParams({ deviceId }, ['deviceId']);
            const result = await deviceService_1.default.getDevice(deviceId);
            this.sendSuccess(res, result.data, 'Device details retrieved successfully', 200, timer.addTimingMetadata({
                deviceId,
                hasConnectionInfo: !!result.data?.connection,
                hasStats: !!result.data?.stats
            }));
        });
        this.deleteDevice = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const { deviceId } = req.params;
            this.logRequest(req, 'deleteDevice', { deviceId });
            this.validateRequiredParams({ deviceId }, ['deviceId']);
            await deviceService_1.default.deleteDevice(deviceId);
            this.sendSuccess(res, null, 'Device deleted successfully', 200, timer.addTimingMetadata());
        });
        this.getSettings = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            this.logRequest(req, 'getSettings');
            const settings = await deviceService_1.default.getSettings();
            this.sendSuccess(res, settings, 'Device settings retrieved successfully', 200, timer.addTimingMetadata());
        });
        this.updateSettings = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const settings = req.body;
            this.logRequest(req, 'updateSettings', { settingsKeys: Object.keys(settings) });
            if (!settings || Object.keys(settings).length === 0) {
                this.sendError(res, 'Settings data is required', 400);
                return;
            }
            await deviceService_1.default.updateSettings(settings);
            this.sendSuccess(res, null, 'Device settings updated successfully', 200, timer.addTimingMetadata());
        });
        this.patchSettings = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const settings = req.body;
            this.logRequest(req, 'patchSettings', { settingsKeys: Object.keys(settings) });
            if (!settings || Object.keys(settings).length === 0) {
                this.sendError(res, 'Settings data is required', 400);
                return;
            }
            await deviceService_1.default.patchSettings(settings);
            this.sendSuccess(res, null, 'Device settings updated successfully', 200, timer.addTimingMetadata());
        });
        this.getHealth = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            this.logRequest(req, 'getHealth');
            const result = await deviceService_1.default.getHealth();
            this.sendSuccess(res, result.data, 'System health retrieved successfully', 200, timer.addTimingMetadata());
        });
        this.getLogs = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const { from, to } = req.query;
            const fromDate = from && typeof from === 'string' ? this.parseDate(from, 'from') : undefined;
            const toDate = to && typeof to === 'string' ? this.parseDate(to, 'to') : undefined;
            this.logRequest(req, 'getLogs', { from: fromDate, to: toDate });
            const result = await deviceService_1.default.getLogs(fromDate, toDate);
            this.sendSuccess(res, result.data, 'System logs retrieved successfully', 200, timer.addTimingMetadata({
                dateRange: { from: fromDate, to: toDate },
                logCount: result.data?.length || 0
            }));
        });
        this.exportInbox = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            const { deviceId } = req.params;
            const { since, until } = req.body;
            this.logRequest(req, 'exportInbox', { deviceId, since, until });
            this.validateRequiredParams({ deviceId, since, until }, ['deviceId', 'since', 'until']);
            const sinceDate = this.parseDate(since, 'since');
            const untilDate = this.parseDate(until, 'until');
            if (sinceDate >= untilDate) {
                this.sendError(res, 'Since date must be before until date', 400);
                return;
            }
            await deviceService_1.default.exportInbox(deviceId, sinceDate, untilDate);
            this.sendSuccess(res, null, 'Inbox export requested successfully', 202, timer.addTimingMetadata({
                deviceId,
                dateRange: { since: sinceDate, until: untilDate }
            }));
        });
        this.getDeviceStats = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            this.logRequest(req, 'getDeviceStats');
            const stats = await deviceService_1.default.getDeviceStats();
            this.sendSuccess(res, stats, 'Device statistics retrieved successfully', 200, timer.addTimingMetadata());
        });
        this.testConnection = this.asyncHandler(async (req, res) => {
            const timer = this.createTimer();
            this.logRequest(req, 'testConnection');
            const isConnected = await deviceService_1.default.testConnection();
            this.sendSuccess(res, {
                connected: isConnected,
                timestamp: new Date().toISOString()
            }, isConnected ? 'Device connection successful' : 'Device connection failed', 200, timer.addTimingMetadata());
        });
    }
}
exports.default = new DeviceController();
//# sourceMappingURL=deviceController.js.map