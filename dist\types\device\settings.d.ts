export declare enum LimitPeriod {
    Disabled = "Disabled",
    PerMinute = "PerMinute",
    PerHour = "PerHour",
    PerDay = "PerDay"
}
export declare enum SimSelectionMode {
    OSDefault = "OSDefault",
    RoundRobin = "RoundRobin",
    Random = "Random"
}
export declare enum LogLevel {
    Debug = "debug",
    Info = "info",
    Warn = "warn",
    Error = "error"
}
export interface SettingsMessages {
    limitPeriod?: LimitPeriod;
    limitValue?: number;
    logLifetimeDays?: number;
    sendIntervalMin?: number;
    sendIntervalMax?: number;
    simSelectionMode?: SimSelectionMode;
    enableDeliveryReports?: boolean;
    maxRetryAttempts?: number;
    retryDelaySeconds?: number;
    queueSizeLimit?: number;
}
export interface SettingsWebhooks {
    internetRequired?: boolean;
    retryCount?: number;
    signingKey?: string;
    timeoutSeconds?: number;
    enabled?: boolean;
    customHeaders?: Record<string, string>;
}
export interface SettingsGateway {
    name?: string;
    cloudUrl?: string;
    privateToken?: string;
    connectionTimeoutSeconds?: number;
    autoReconnect?: boolean;
    reconnectDelaySeconds?: number;
    heartbeatIntervalSeconds?: number;
}
export interface SettingsEncryption {
    enabled?: boolean;
    passphrase?: string;
    algorithm?: 'AES-256' | 'AES-128';
    keyRotationDays?: number;
}
export interface SettingsLogs {
    ttl?: number;
    lifetimeDays?: number;
    level?: LogLevel;
    enableFileLogging?: boolean;
    maxFileSizeMB?: number;
    maxFiles?: number;
}
export interface SettingsPing {
    enabled?: boolean;
    interval?: number;
    intervalSeconds?: number;
    timeoutSeconds?: number;
    enableFailureAlerts?: boolean;
    maxFailuresBeforeAlert?: number;
}
export interface DeviceSettings {
    messages?: SettingsMessages;
    webhooks?: SettingsWebhooks;
    gateway?: SettingsGateway;
    encryption?: SettingsEncryption;
    logs?: SettingsLogs;
    ping?: SettingsPing;
    version?: string;
    lastUpdated?: Date;
    metadata?: Record<string, any>;
}
export interface UpdateSettingsRequest extends DeviceSettings {
    validate?: boolean;
}
export interface PatchSettingsRequest extends Partial<DeviceSettings> {
    validate?: boolean;
}
export interface SettingsValidationError {
    field: string;
    message: string;
    value: any;
    suggestion?: string;
}
export interface SettingsValidationResult {
    valid: boolean;
    errors: SettingsValidationError[];
    warnings: SettingsValidationError[];
}
export interface DeviceSettingsResponse {
    settings: DeviceSettings;
    defaults: DeviceSettings;
    options: {
        limitPeriods: LimitPeriod[];
        simSelectionModes: SimSelectionMode[];
        logLevels: LogLevel[];
        encryptionAlgorithms: string[];
    };
    metadata: {
        version: string;
        lastUpdated: Date;
        isDefault: boolean;
        hasUnsavedChanges: boolean;
    };
}
export interface SettingsExport {
    settings: DeviceSettings;
    metadata: {
        exportedAt: Date;
        version: string;
        deviceId: string;
        deviceName: string;
    };
}
export interface SettingsImport {
    settings: Partial<DeviceSettings>;
    options: {
        merge: boolean;
        validate: boolean;
        backup: boolean;
    };
}
//# sourceMappingURL=settings.d.ts.map