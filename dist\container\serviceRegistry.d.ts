import { IDeviceService } from '../types';
export declare function registerServices(): void;
export declare function initializeServices(): Promise<void>;
export declare function performHealthCheck(): Promise<{
    healthy: boolean;
    services: Record<string, boolean>;
}>;
export declare function shutdownServices(): Promise<void>;
export declare function getService<T>(name: string): T;
export declare function createRequestScope(): import("./DIContainer").DIContainer;
export declare const ServiceFactory: {
    getDeviceService(): IDeviceService;
    getSmsService(): unknown;
    getLineService(): unknown;
    getCacheService(): unknown;
    getMetricsService(): unknown;
};
export declare function createServiceMiddleware(): (req: any, res: any, next: any) => void;
//# sourceMappingURL=serviceRegistry.d.ts.map