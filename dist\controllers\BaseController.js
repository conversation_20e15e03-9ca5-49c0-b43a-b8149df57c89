"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
const responseHelpers_1 = require("../utils/responseHelpers");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = __importDefault(require("../utils/logger"));
class BaseController {
    constructor() {
        this.logger = logger_1.default;
    }
    sendSuccess(res, data, message, statusCode = 200, metadata) {
        const response = (0, responseHelpers_1.createSuccessResponse)(data, message, metadata);
        res.status(statusCode).json(response);
    }
    sendPaginated(res, data, totalCount, pagination, filters, sort, metadata) {
        const validatedPagination = (0, responseHelpers_1.validatePaginationOptions)(pagination);
        const totalPages = Math.ceil(totalCount / validatedPagination.limit);
        const paginationMeta = {
            page: validatedPagination.page,
            limit: validatedPagination.limit,
            total: totalCount,
            totalPages,
            hasNext: validatedPagination.page < totalPages,
            hasPrev: validatedPagination.page > 1,
            count: data.length,
            offset: (validatedPagination.page - 1) * validatedPagination.limit
        };
        const response = (0, responseHelpers_1.createPaginatedResponse)(data, paginationMeta, filters, sort, metadata);
        res.status(200).json(response);
    }
    sendError(res, error, statusCode = 500, details) {
        if (error instanceof errorHandler_1.AppError) {
            const errorResponse = {
                success: false,
                error: error,
                timestamp: new Date().toISOString(),
                requestId: error.requestId || `req_${Date.now()}`
            };
            res.status(error.statusCode).json(errorResponse);
        }
        else {
            const response = (0, responseHelpers_1.createErrorResponse)(error, statusCode, details);
            res.status(statusCode).json(response);
        }
    }
    sendCreated(res, data, message = 'Resource created successfully', metadata) {
        this.sendSuccess(res, data, message, 201, metadata);
    }
    sendNoContent(res) {
        res.status(204).send();
    }
    getPaginationOptions(req) {
        const { page, limit, sortBy, sortOrder } = req.query;
        const options = {};
        if (page)
            options.page = parseInt(page, 10);
        if (limit)
            options.limit = parseInt(limit, 10);
        if (sortBy)
            options.sortBy = sortBy;
        if (sortOrder)
            options.sortOrder = sortOrder;
        return (0, responseHelpers_1.validatePaginationOptions)(options);
    }
    getFilters(req, allowedFilters) {
        const filters = {};
        allowedFilters.forEach(key => {
            if (req.query[key] !== undefined) {
                filters[key] = req.query[key];
            }
        });
        return filters;
    }
    getRequestId(req) {
        return req.headers['x-request-id'] ||
            `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    logRequest(req, action, metadata) {
        this.logger.info(`Controller action: ${action}`, {
            method: req.method,
            url: req.url,
            requestId: this.getRequestId(req),
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            ...metadata
        });
    }
    validateRequiredParams(params, required) {
        const missing = required.filter(key => params[key] === undefined || params[key] === null || params[key] === '');
        if (missing.length > 0) {
            throw new errorHandler_1.AppError(`Missing required parameters: ${missing.join(', ')}`, 400, true, {
                details: { missingParams: missing }
            });
        }
    }
    createTimer() {
        return new responseHelpers_1.ResponseTimer();
    }
    asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res)).catch(next);
        };
    }
    parseDate(dateString, paramName) {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            throw new errorHandler_1.AppError(`Invalid date format for parameter '${paramName}'`, 400, true, {
                details: {
                    paramName,
                    value: dateString,
                    expectedFormat: 'ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)'
                }
            });
        }
        return date;
    }
    sanitizeString(value, maxLength, paramName) {
        if (typeof value !== 'string') {
            throw new errorHandler_1.AppError(`Parameter '${paramName}' must be a string`, 400, true, { details: { paramName, type: typeof value } });
        }
        const sanitized = value.trim();
        if (sanitized.length > maxLength) {
            throw new errorHandler_1.AppError(`Parameter '${paramName}' exceeds maximum length of ${maxLength} characters`, 400, true, { details: { paramName, length: sanitized.length, maxLength } });
        }
        return sanitized;
    }
}
exports.BaseController = BaseController;
//# sourceMappingURL=BaseController.js.map