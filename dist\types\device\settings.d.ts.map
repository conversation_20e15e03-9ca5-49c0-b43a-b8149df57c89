{"version": 3, "file": "settings.d.ts", "sourceRoot": "", "sources": ["../../../src/types/device/settings.ts"], "names": [], "mappings": "AAMA,oBAAY,WAAW;IACrB,QAAQ,aAAa;IACrB,SAAS,cAAc;IACvB,OAAO,YAAY;IACnB,MAAM,WAAW;CAClB;AAGD,oBAAY,gBAAgB;IAC1B,SAAS,cAAc;IACvB,UAAU,eAAe;IACzB,MAAM,WAAW;CAClB;AAGD,oBAAY,QAAQ;IAClB,KAAK,UAAU;IACf,IAAI,SAAS;IACb,IAAI,SAAS;IACb,KAAK,UAAU;CAChB;AAOD,MAAM,WAAW,gBAAgB;IAE/B,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAE3B,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAKD,MAAM,WAAW,gBAAgB;IAE/B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,aAAa,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACxC;AAKD,MAAM,WAAW,eAAe;IAE9B,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAElC,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAE/B,wBAAwB,CAAC,EAAE,MAAM,CAAC;CACnC;AAKD,MAAM,WAAW,kBAAkB;IAEjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;IAElC,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAKD,MAAM,WAAW,YAAY;IAE3B,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB,KAAK,CAAC,EAAE,QAAQ,CAAC;IAEjB,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAE5B,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAKD,MAAM,WAAW,YAAY;IAE3B,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAE9B,sBAAsB,CAAC,EAAE,MAAM,CAAC;CACjC;AAKD,MAAM,WAAW,cAAc;IAE7B,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAE5B,QAAQ,CAAC,EAAE,gBAAgB,CAAC;IAE5B,OAAO,CAAC,EAAE,eAAe,CAAC;IAE1B,UAAU,CAAC,EAAE,kBAAkB,CAAC;IAEhC,IAAI,CAAC,EAAE,YAAY,CAAC;IAEpB,IAAI,CAAC,EAAE,YAAY,CAAC;IAEpB,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB,WAAW,CAAC,EAAE,IAAI,CAAC;IAEnB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAGD,MAAM,WAAW,qBAAsB,SAAQ,cAAc;IAE3D,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,oBAAqB,SAAQ,OAAO,CAAC,cAAc,CAAC;IAEnE,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAGD,MAAM,WAAW,uBAAuB;IAEtC,KAAK,EAAE,MAAM,CAAC;IAEd,OAAO,EAAE,MAAM,CAAC;IAEhB,KAAK,EAAE,GAAG,CAAC;IAEX,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,wBAAwB;IAEvC,KAAK,EAAE,OAAO,CAAC;IAEf,MAAM,EAAE,uBAAuB,EAAE,CAAC;IAElC,QAAQ,EAAE,uBAAuB,EAAE,CAAC;CACrC;AAGD,MAAM,WAAW,sBAAsB;IAErC,QAAQ,EAAE,cAAc,CAAC;IAEzB,QAAQ,EAAE,cAAc,CAAC;IAEzB,OAAO,EAAE;QACP,YAAY,EAAE,WAAW,EAAE,CAAC;QAC5B,iBAAiB,EAAE,gBAAgB,EAAE,CAAC;QACtC,SAAS,EAAE,QAAQ,EAAE,CAAC;QACtB,oBAAoB,EAAE,MAAM,EAAE,CAAC;KAChC,CAAC;IAEF,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,IAAI,CAAC;QAClB,SAAS,EAAE,OAAO,CAAC;QACnB,iBAAiB,EAAE,OAAO,CAAC;KAC5B,CAAC;CACH;AAGD,MAAM,WAAW,cAAc;IAE7B,QAAQ,EAAE,cAAc,CAAC;IAEzB,QAAQ,EAAE;QACR,UAAU,EAAE,IAAI,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC;QACjB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,cAAc;IAE7B,QAAQ,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IAElC,OAAO,EAAE;QAEP,KAAK,EAAE,OAAO,CAAC;QAEf,QAAQ,EAAE,OAAO,CAAC;QAElB,MAAM,EAAE,OAAO,CAAC;KACjB,CAAC;CACH"}