import { PaginationMeta } from './common';
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp?: string;
    requestId?: string;
    metadata?: Record<string, any>;
}
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: PaginationMeta;
    filters?: Record<string, any>;
    sort?: {
        field: string;
        order: 'asc' | 'desc';
    };
}
export interface HttpClient {
    get<T>(url: string, headers?: Record<string, string>): Promise<T>;
    post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
    put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
    patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
    delete<T>(url: string, headers?: Record<string, string>): Promise<T>;
    testConnection?(): Promise<boolean>;
    getBaseUrl?(): string;
}
export interface BaseRequest {
    timestamp?: string;
    requestId?: string;
    [key: string]: any;
}
export interface BaseResponse {
    timestamp: string;
    requestId?: string;
    [key: string]: any;
}
export interface ErrorResponse {
    code?: string;
    error: string;
    message?: string;
    details?: any;
    fieldErrors?: Record<string, string[]>;
    timestamp: string;
    requestId?: string;
    stack?: string;
}
export declare enum HealthStatus {
    Pass = "pass",
    Warn = "warn",
    Fail = "fail"
}
export interface HealthCheck {
    status: HealthStatus;
    description: string;
    observedValue: number;
    observedUnit: string;
    threshold?: {
        min?: number;
        max?: number;
    };
    lastChecked: string;
    metadata?: Record<string, any>;
}
export interface HealthResponse {
    status: HealthStatus;
    version: string;
    releaseId: number;
    checks: Record<string, HealthCheck>;
    uptime: number;
    timestamp: string;
    environment?: string;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
    rule?: string;
}
export interface ValidationResult {
    valid: boolean;
    errors: ValidationError[];
}
export interface ApiEndpoint {
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    path: string;
    description: string;
    requestSchema?: any;
    responseSchema?: any;
    requiresAuth?: boolean;
    rateLimit?: {
        requests: number;
        window: string;
    };
}
//# sourceMappingURL=api.d.ts.map