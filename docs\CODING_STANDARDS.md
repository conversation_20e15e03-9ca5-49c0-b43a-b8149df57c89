# Coding Standards and Best Practices

This document outlines the coding standards and best practices for the SMS Gateway Backend project.

## Table of Contents

1. [General Principles](#general-principles)
2. [TypeScript Standards](#typescript-standards)
3. [Code Organization](#code-organization)
4. [Naming Conventions](#naming-conventions)
5. [Documentation Standards](#documentation-standards)
6. [Error Handling](#error-handling)
7. [Testing Guidelines](#testing-guidelines)
8. [Performance Considerations](#performance-considerations)

## General Principles

### 1. Code Readability
- Write code that tells a story
- Use descriptive names for variables, functions, and classes
- Keep functions small and focused on a single responsibility
- Use consistent formatting and indentation

### 2. Maintainability
- Follow the DRY (Don't Repeat Yourself) principle
- Use dependency injection for better testability
- Implement proper separation of concerns
- Write comprehensive tests

### 3. Performance
- Optimize for readability first, performance second
- Use appropriate data structures and algorithms
- Implement caching where beneficial
- Monitor and measure performance impacts

## TypeScript Standards

### Type Definitions
```typescript
// ✅ Good: Comprehensive interface with documentation
interface DeviceConfiguration {
  /** Device unique identifier */
  id: string;
  /** Human-readable device name */
  name: string;
  /** Device capabilities and features */
  capabilities: DeviceCapabilities;
  /** Optional metadata */
  metadata?: Record<string, any>;
}

// ❌ Bad: Vague types without documentation
interface Config {
  id: any;
  data: object;
}
```

### Generic Types
```typescript
// ✅ Good: Well-constrained generics
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// ✅ Good: Utility types for better type safety
type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
```

### Enums vs Union Types
```typescript
// ✅ Good: Use enums for stable, well-defined sets
export enum DeviceStatus {
  Online = 'online',
  Offline = 'offline',
  Maintenance = 'maintenance'
}

// ✅ Good: Use union types for simple alternatives
type SortOrder = 'asc' | 'desc';
```

## Code Organization

### Directory Structure
```
src/
├── controllers/          # HTTP request handlers
├── services/            # Business logic layer
├── types/               # Type definitions
│   ├── base/           # Common base types
│   ├── device/         # Device-related types
│   ├── services/       # Service interfaces
│   └── errors/         # Error types
├── utils/              # Utility functions
├── middleware/         # Express middleware
├── config/             # Configuration management
└── container/          # Dependency injection
```

### File Naming
- Use camelCase for files: `deviceService.ts`
- Use PascalCase for classes: `DeviceController.ts`
- Use kebab-case for configuration: `service-config.ts`

### Import Organization
```typescript
// 1. Node.js built-in modules
import { Request, Response } from 'express';

// 2. Third-party modules
import axios from 'axios';

// 3. Internal modules (absolute imports)
import { DeviceService } from '../services/deviceService';
import { ApiResponse } from '../types';

// 4. Relative imports
import { validateInput } from './validators';
```

## Naming Conventions

### Variables and Functions
```typescript
// ✅ Good: Descriptive and clear
const deviceConnectionTimeout = 30000;
const isDeviceOnline = (device: Device) => device.status === 'online';

// ❌ Bad: Abbreviated or unclear
const dct = 30000;
const chk = (d: any) => d.s === 'on';
```

### Classes and Interfaces
```typescript
// ✅ Good: Clear purpose and responsibility
class DeviceConnectionManager {
  private readonly connectionPool: Map<string, Connection>;
}

interface DeviceHealthCheckResult {
  deviceId: string;
  isHealthy: boolean;
  lastChecked: Date;
}
```

### Constants
```typescript
// ✅ Good: SCREAMING_SNAKE_CASE for constants
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_TIMEOUT_MS = 30000;

// ✅ Good: Grouped constants in objects
const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  INTERNAL_ERROR: 500
} as const;
```

## Documentation Standards

### JSDoc Comments
```typescript
/**
 * Retrieves device information with comprehensive details
 * 
 * This method fetches device data from the SMS Gateway and enriches it
 * with additional metadata, connection status, and performance metrics.
 * 
 * @param deviceId - Unique device identifier
 * @param options - Optional retrieval options
 * @param options.includeStats - Whether to include performance statistics
 * @param options.includeHistory - Whether to include activity history
 * @returns Promise resolving to detailed device information
 * 
 * @throws {DeviceNotFoundError} When device doesn't exist
 * @throws {NetworkError} When unable to connect to SMS Gateway
 * 
 * @example
 * ```typescript
 * const device = await deviceService.getDevice('device-123', {
 *   includeStats: true,
 *   includeHistory: false
 * });
 * console.log(`Device: ${device.name}, Status: ${device.status}`);
 * ```
 * 
 * @since 1.0.0
 */
async getDevice(
  deviceId: string,
  options?: {
    includeStats?: boolean;
    includeHistory?: boolean;
  }
): Promise<DeviceDetails> {
  // Implementation...
}
```

### README Documentation
Each module should have a README.md with:
- Purpose and overview
- Installation/setup instructions
- Usage examples
- API documentation
- Contributing guidelines

## Error Handling

### Error Types
```typescript
// ✅ Good: Specific error types with context
class DeviceNotFoundError extends AppError {
  constructor(deviceId: string) {
    super(
      `Device with ID '${deviceId}' not found`,
      404,
      true,
      {
        code: 'DEVICE_NOT_FOUND',
        deviceId,
        userMessage: 'The requested device could not be found.'
      }
    );
  }
}
```

### Error Handling Patterns
```typescript
// ✅ Good: Comprehensive error handling
async function processDevice(deviceId: string): Promise<ProcessResult> {
  try {
    const device = await deviceService.getDevice(deviceId);
    
    if (!device.isOnline) {
      throw new DeviceOfflineError(deviceId);
    }
    
    return await processDeviceData(device);
    
  } catch (error) {
    if (error instanceof DeviceNotFoundError) {
      logger.warn('Device not found during processing', { deviceId });
      throw error;
    }
    
    if (error instanceof NetworkError && error.retryable) {
      logger.info('Retryable network error, scheduling retry', { deviceId });
      throw new RetryableError(error.message, error);
    }
    
    logger.error('Unexpected error during device processing', {
      deviceId,
      error: error.message,
      stack: error.stack
    });
    
    throw new InternalError('Device processing failed');
  }
}
```

## Testing Guidelines

### Test Structure
```typescript
describe('DeviceService', () => {
  describe('getDevice', () => {
    it('should return device details when device exists', async () => {
      // Arrange
      const deviceId = 'test-device-123';
      const expectedDevice = createMockDevice(deviceId);
      mockSmsGateway.getDevices.mockResolvedValue([expectedDevice]);
      
      // Act
      const result = await deviceService.getDevice(deviceId);
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.id).toBe(deviceId);
      expect(result.data?.name).toBe(expectedDevice.name);
    });
    
    it('should throw DeviceNotFoundError when device does not exist', async () => {
      // Arrange
      const deviceId = 'non-existent-device';
      mockSmsGateway.getDevices.mockResolvedValue([]);
      
      // Act & Assert
      await expect(deviceService.getDevice(deviceId))
        .rejects
        .toThrow(DeviceNotFoundError);
    });
  });
});
```

### Test Naming
- Use descriptive test names that explain the scenario
- Follow the pattern: "should [expected behavior] when [condition]"
- Group related tests using `describe` blocks

## Performance Considerations

### Async/Await Best Practices
```typescript
// ✅ Good: Parallel execution when possible
const [devices, settings, stats] = await Promise.all([
  deviceService.getDevices(),
  deviceService.getSettings(),
  deviceService.getStats()
]);

// ❌ Bad: Sequential execution when not necessary
const devices = await deviceService.getDevices();
const settings = await deviceService.getSettings();
const stats = await deviceService.getStats();
```

### Memory Management
```typescript
// ✅ Good: Proper cleanup and resource management
class DeviceConnectionPool {
  private connections = new Map<string, Connection>();
  
  async cleanup(): Promise<void> {
    for (const [id, connection] of this.connections) {
      await connection.close();
    }
    this.connections.clear();
  }
}
```

### Caching Strategies
```typescript
// ✅ Good: Intelligent caching with TTL
class DeviceService {
  private cache = new Map<string, { data: Device; expires: number }>();
  
  async getDevice(deviceId: string): Promise<Device> {
    const cached = this.cache.get(deviceId);
    
    if (cached && Date.now() < cached.expires) {
      return cached.data;
    }
    
    const device = await this.fetchDeviceFromGateway(deviceId);
    
    this.cache.set(deviceId, {
      data: device,
      expires: Date.now() + 300000 // 5 minutes
    });
    
    return device;
  }
}
```

## Code Review Checklist

- [ ] Code follows naming conventions
- [ ] Functions have single responsibility
- [ ] Proper error handling implemented
- [ ] JSDoc documentation provided
- [ ] Types are properly defined
- [ ] Tests cover main scenarios
- [ ] Performance considerations addressed
- [ ] Security best practices followed
- [ ] No code duplication
- [ ] Consistent formatting applied
