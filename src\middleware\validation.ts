import { Request, Response, NextFunction } from 'express';
import { AppError, validationErrorHandler } from './errorHandler';

/**
 * Enhanced validation interfaces and types
 */
interface ValidationError {
  field: string;
  message: string;
  value?: any;
  rule?: string;
}

interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Base validator class for common validation patterns
 */
abstract class BaseValidator {
  protected errors: ValidationError[] = [];

  protected addError(field: string, message: string, value?: any, rule?: string): void {
    this.errors.push({ field, message, value, rule });
  }

  protected isValid(): boolean {
    return this.errors.length === 0;
  }

  protected getResult(): ValidationResult {
    return {
      valid: this.isValid(),
      errors: [...this.errors]
    };
  }

  protected reset(): void {
    this.errors = [];
  }
}

export const validatePhoneNumber = (phoneNumber: string): boolean => {
  // Basic phone number validation (E.164 format)
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

export const validateSMSMessage = (req: Request, _res: Response, next: NextFunction): void => {
  const { phoneNumbers, message } = req.body;

  if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
    throw new AppError('Phone numbers are required and must be a non-empty array', 400);
  }

  if (!message || typeof message !== 'string' || message.trim().length === 0) {
    throw new AppError('Message is required and must be a non-empty string', 400);
  }

  if (message.length > 1600) {
    throw new AppError('Message is too long (maximum 1600 characters)', 400);
  }

  if (phoneNumbers.length > 10) {
    throw new AppError('Too many phone numbers (maximum 10 per request)', 400);
  }

  // Validate each phone number
  const invalidNumbers = phoneNumbers.filter((num: string) => !validatePhoneNumber(num));
  if (invalidNumbers.length > 0) {
    throw new AppError(`Invalid phone numbers: ${invalidNumbers.join(', ')}`, 400);
  }

  next();
};

export const validatePagination = (req: Request, _res: Response, next: NextFunction): void => {
  const { page, limit } = req.query;

  if (page && (isNaN(Number(page)) || Number(page) < 1)) {
    throw new AppError('Page must be a positive integer', 400);
  }

  if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
    throw new AppError('Limit must be a positive integer between 1 and 100', 400);
  }

  next();
};

export const validateMessageId = (req: Request, _res: Response, next: NextFunction): void => {
  const { messageId } = req.params;

  if (!messageId || typeof messageId !== 'string' || messageId.trim().length === 0) {
    throw new AppError('Message ID is required', 400);
  }

  next();
};

export const validateWebhookSignature = (secret: string) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    const signature = req.headers['x-webhook-signature'] as string;
    
    if (!signature) {
      throw new AppError('Webhook signature is required', 401);
    }

    // In a real implementation, you would verify the signature
    // using HMAC-SHA256 with the webhook secret
    // For now, we'll just check if the signature matches the secret
    if (signature !== secret) {
      throw new AppError('Invalid webhook signature', 401);
    }

    next();
  };
};

export const validateLineSignature = (_channelSecret: string) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    const signature = req.headers['x-line-signature'] as string;

    if (!signature) {
      throw new AppError('LINE signature is required', 401);
    }

    // LINE signature validation would be implemented here
    // using the @line/bot-sdk's validateSignature function
    // For now, we'll skip the actual validation

    next();
  };
};

/**
 * Enhanced device validator class
 */
export class DeviceValidator extends BaseValidator {
  /**
   * Validate device ID parameter
   */
  validateDeviceId(deviceId: any): ValidationResult {
    this.reset();

    if (!deviceId) {
      this.addError('deviceId', 'Device ID is required', deviceId, 'required');
    } else if (typeof deviceId !== 'string') {
      this.addError('deviceId', 'Device ID must be a string', deviceId, 'type');
    } else if (deviceId.trim().length === 0) {
      this.addError('deviceId', 'Device ID cannot be empty', deviceId, 'empty');
    } else if (deviceId.length > 100) {
      this.addError('deviceId', 'Device ID cannot exceed 100 characters', deviceId, 'maxLength');
    }

    return this.getResult();
  }

  /**
   * Validate device settings
   */
  validateDeviceSettings(settings: any): ValidationResult {
    this.reset();

    if (!settings || typeof settings !== 'object') {
      this.addError('settings', 'Settings must be an object', settings, 'type');
      return this.getResult();
    }

    // Validate messages settings
    if (settings.messages && typeof settings.messages !== 'object') {
      this.addError('messages', 'Messages settings must be an object', settings.messages, 'type');
    }

    // Validate webhooks settings
    if (settings.webhooks) {
      if (typeof settings.webhooks !== 'object') {
        this.addError('webhooks', 'Webhooks settings must be an object', settings.webhooks, 'type');
      } else if (settings.webhooks.url && typeof settings.webhooks.url !== 'string') {
        this.addError('webhooks.url', 'Webhook URL must be a string', settings.webhooks.url, 'type');
      }
    }

    return this.getResult();
  }
}

/**
 * Enhanced validation middleware
 */
export class ValidationMiddleware {
  private static deviceValidator = new DeviceValidator();

  /**
   * Validate device ID parameter
   */
  static validateDeviceId() {
    return (req: Request, res: Response, next: NextFunction) => {
      const { deviceId } = req.params;
      const result = ValidationMiddleware.deviceValidator.validateDeviceId(deviceId);

      if (!result.valid) {
        const error = validationErrorHandler(result.errors);
        return next(error);
      }

      next();
    };
  }

  /**
   * Validate device settings in request body
   */
  static validateDeviceSettings() {
    return (req: Request, res: Response, next: NextFunction) => {
      const result = ValidationMiddleware.deviceValidator.validateDeviceSettings(req.body);

      if (!result.valid) {
        const error = validationErrorHandler(result.errors);
        return next(error);
      }

      next();
    };
  }

  /**
   * Validate date range in request body
   */
  static validateDateRange() {
    return (req: Request, res: Response, next: NextFunction) => {
      const { since, until } = req.body;
      const errors: ValidationError[] = [];

      if (!since) {
        errors.push({ field: 'since', message: 'Since date is required', value: since, rule: 'required' });
      } else {
        const sinceDate = new Date(since);
        if (isNaN(sinceDate.getTime())) {
          errors.push({ field: 'since', message: 'Invalid since date format', value: since, rule: 'date' });
        }
      }

      if (!until) {
        errors.push({ field: 'until', message: 'Until date is required', value: until, rule: 'required' });
      } else {
        const untilDate = new Date(until);
        if (isNaN(untilDate.getTime())) {
          errors.push({ field: 'until', message: 'Invalid until date format', value: until, rule: 'date' });
        }
      }

      if (errors.length === 0 && since && until) {
        const sinceDate = new Date(since);
        const untilDate = new Date(until);

        if (sinceDate >= untilDate) {
          errors.push({ field: 'dateRange', message: 'Since date must be before until date', rule: 'dateRange' });
        }
      }

      if (errors.length > 0) {
        const error = validationErrorHandler(errors);
        return next(error);
      }

      next();
    };
  }
}
