/**
 * Response helper utilities for consistent API responses
 * Provides standardized response formats optimized for UI consumption
 */

import { 
  ApiResponse, 
  PaginatedResponse, 
  ErrorResponse,
  PaginationMeta,
  PaginationOptions 
} from '../types';

/**
 * Generate a unique request ID for tracking
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Create a successful API response
 * 
 * @param data - Response data
 * @param message - Optional success message
 * @param metadata - Optional additional metadata
 * @returns Standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  metadata?: Record<string, any>
): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
    requestId: generateRequestId(),
    metadata
  };
}

/**
 * Create a paginated API response
 * 
 * @param data - Array of items
 * @param pagination - Pagination metadata
 * @param filters - Applied filters
 * @param sort - Sort information
 * @param metadata - Additional metadata
 * @returns Standardized paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: PaginationMeta,
  filters?: Record<string, any>,
  sort?: { field: string; order: 'asc' | 'desc' },
  metadata?: Record<string, any>
): PaginatedResponse<T> {
  return {
    success: true,
    data,
    pagination,
    filters,
    sort,
    timestamp: new Date().toISOString(),
    requestId: generateRequestId(),
    metadata
  };
}

/**
 * Create an error response
 * 
 * @param error - Error message
 * @param statusCode - HTTP status code
 * @param details - Additional error details
 * @param fieldErrors - Field-specific validation errors
 * @returns Standardized error response
 */
export function createErrorResponse(
  error: string,
  statusCode: number = 500,
  details?: any,
  fieldErrors?: Record<string, string[]>
): ErrorResponse {
  return {
    success: false,
    error: {
      code: `E${statusCode}`,
      message: error,
      userMessage: getUserFriendlyMessage(error, statusCode),
      category: getErrorCategory(statusCode),
      severity: getErrorSeverity(statusCode),
      statusCode,
      details,
      timestamp: new Date(),
      requestId: generateRequestId(),
      fieldErrors
    },
    timestamp: new Date().toISOString(),
    requestId: generateRequestId()
  };
}

/**
 * Apply pagination to an array of items
 * 
 * @param items - Array of items to paginate
 * @param options - Pagination options
 * @returns Paginated result with metadata
 */
export function applyPagination<T>(
  items: T[],
  options?: PaginationOptions
): { data: T[]; pagination: PaginationMeta } {
  const page = Math.max(options?.page || 1, 1);
  const limit = Math.min(Math.max(options?.limit || 20, 1), 100);
  const offset = (page - 1) * limit;
  
  const paginatedItems = items.slice(offset, offset + limit);
  const total = items.length;
  const totalPages = Math.ceil(total / limit);

  return {
    data: paginatedItems,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
      count: paginatedItems.length,
      offset
    }
  };
}

/**
 * Get user-friendly error message based on error and status code
 * 
 * @param error - Original error message
 * @param statusCode - HTTP status code
 * @returns User-friendly error message
 */
function getUserFriendlyMessage(error: string, statusCode: number): string {
  const errorLower = error.toLowerCase();
  
  // Common error patterns
  if (errorLower.includes('not found') || statusCode === 404) {
    return 'The requested resource could not be found.';
  }
  
  if (errorLower.includes('unauthorized') || statusCode === 401) {
    return 'You are not authorized to perform this action.';
  }
  
  if (errorLower.includes('forbidden') || statusCode === 403) {
    return 'You do not have permission to access this resource.';
  }
  
  if (errorLower.includes('validation') || statusCode === 400) {
    return 'Please check your input and correct any errors.';
  }
  
  if (errorLower.includes('rate limit') || statusCode === 429) {
    return 'You are making requests too quickly. Please wait a moment and try again.';
  }
  
  if (errorLower.includes('network') || errorLower.includes('connection')) {
    return 'Unable to connect to the service. Please check your connection and try again.';
  }
  
  if (statusCode >= 500) {
    return 'Something went wrong on our end. Please try again later.';
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Get error category based on status code
 */
function getErrorCategory(statusCode: number): string {
  if (statusCode === 400) return 'validation';
  if (statusCode === 401) return 'authentication';
  if (statusCode === 403) return 'authorization';
  if (statusCode === 404) return 'not_found';
  if (statusCode === 409) return 'conflict';
  if (statusCode === 429) return 'rate_limit';
  if (statusCode >= 500) return 'internal';
  return 'unknown';
}

/**
 * Get error severity based on status code
 */
function getErrorSeverity(statusCode: number): string {
  if (statusCode >= 500) return 'high';
  if (statusCode >= 400) return 'medium';
  return 'low';
}

/**
 * Validate pagination options
 * 
 * @param options - Pagination options to validate
 * @returns Validated and normalized pagination options
 */
export function validatePaginationOptions(options?: PaginationOptions): PaginationOptions {
  if (!options) {
    return { page: 1, limit: 20 };
  }

  return {
    page: Math.max(options.page || 1, 1),
    limit: Math.min(Math.max(options.limit || 20, 1), 100),
    sortBy: options.sortBy,
    sortOrder: options.sortOrder || 'desc'
  };
}

/**
 * Create operation result for service methods
 * 
 * @param success - Whether operation was successful
 * @param data - Result data
 * @param error - Error message if failed
 * @param metadata - Additional metadata
 * @returns Operation result
 */
export function createOperationResult<T = any>(
  success: boolean,
  data?: T,
  error?: string,
  metadata?: Record<string, any>
): { success: boolean; data?: T; error?: string; metadata?: Record<string, any> } {
  return {
    success,
    data,
    error,
    metadata: {
      ...metadata,
      timestamp: new Date().toISOString(),
      requestId: generateRequestId()
    }
  };
}

/**
 * Response timing utility for performance monitoring
 */
export class ResponseTimer {
  private startTime: number;
  
  constructor() {
    this.startTime = Date.now();
  }
  
  /**
   * Get elapsed time in milliseconds
   */
  getElapsed(): number {
    return Date.now() - this.startTime;
  }
  
  /**
   * Add timing metadata to response
   */
  addTimingMetadata(metadata: Record<string, any> = {}): Record<string, any> {
    return {
      ...metadata,
      processingTime: this.getElapsed(),
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Health check response helper
 */
export function createHealthResponse(
  status: 'pass' | 'warn' | 'fail',
  checks: Record<string, any>,
  version: string = '1.0.0'
): any {
  return {
    status,
    version,
    releaseId: Date.now(),
    checks,
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  };
}
