/**
 * Enhanced error types for better error handling and user experience
 * Provides comprehensive error classification and user-friendly messages
 */

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  Critical = 'critical'
}

/**
 * Error categories for better classification
 */
export enum ErrorCategory {
  Validation = 'validation',
  Authentication = 'authentication',
  Authorization = 'authorization',
  NotFound = 'not_found',
  Conflict = 'conflict',
  RateLimit = 'rate_limit',
  External = 'external',
  Internal = 'internal',
  Network = 'network',
  Timeout = 'timeout',
  Configuration = 'configuration'
}

/**
 * Base error interface with comprehensive information
 */
export interface BaseError {
  /** Error code for programmatic handling */
  code: string;
  /** Error message for developers */
  message: string;
  /** User-friendly error message */
  userMessage: string;
  /** Error category */
  category: ErrorCategory;
  /** Error severity */
  severity: ErrorSeverity;
  /** HTTP status code */
  statusCode: number;
  /** Additional error details */
  details?: Record<string, any>;
  /** Timestamp when error occurred */
  timestamp: Date;
  /** Request ID for tracking */
  requestId?: string;
  /** Stack trace (development only) */
  stack?: string;
}

/**
 * Validation error with field-specific information
 */
export interface ValidationError extends BaseError {
  category: ErrorCategory.Validation;
  /** Field-specific validation errors */
  fieldErrors: FieldError[];
}

/**
 * Field validation error
 */
export interface FieldError {
  /** Field name */
  field: string;
  /** Field value that failed validation */
  value: any;
  /** Validation error message */
  message: string;
  /** Validation rule that failed */
  rule: string;
  /** Additional validation context */
  context?: Record<string, any>;
}

/**
 * External service error
 */
export interface ExternalServiceError extends BaseError {
  category: ErrorCategory.External;
  /** External service name */
  serviceName: string;
  /** External service endpoint */
  endpoint?: string;
  /** External service response code */
  externalCode?: string;
  /** External service response message */
  externalMessage?: string;
  /** Whether the error is retryable */
  retryable: boolean;
  /** Suggested retry delay in seconds */
  retryAfter?: number;
}

/**
 * Network error
 */
export interface NetworkError extends BaseError {
  category: ErrorCategory.Network;
  /** Network operation that failed */
  operation: 'connect' | 'read' | 'write' | 'timeout';
  /** Target host */
  host?: string;
  /** Target port */
  port?: number;
  /** Whether the error is retryable */
  retryable: boolean;
}

/**
 * Configuration error
 */
export interface ConfigurationError extends BaseError {
  category: ErrorCategory.Configuration;
  /** Configuration key that is invalid */
  configKey: string;
  /** Current invalid value */
  currentValue: any;
  /** Expected value format */
  expectedFormat: string;
  /** Suggested fix */
  suggestion: string;
}

/**
 * Error response for API endpoints
 */
export interface ErrorResponse {
  /** Whether the request was successful */
  success: false;
  /** Error information */
  error: BaseError;
  /** Response timestamp */
  timestamp: string;
  /** Request ID for tracking */
  requestId?: string;
  /** Additional context */
  context?: Record<string, any>;
}

/**
 * Error factory for creating standardized errors
 */
export interface ErrorFactory {
  /**
   * Create a validation error
   */
  validation(message: string, fieldErrors: FieldError[], details?: Record<string, any>): ValidationError;
  
  /**
   * Create an authentication error
   */
  authentication(message: string, details?: Record<string, any>): BaseError;
  
  /**
   * Create an authorization error
   */
  authorization(message: string, details?: Record<string, any>): BaseError;
  
  /**
   * Create a not found error
   */
  notFound(resource: string, identifier?: string): BaseError;
  
  /**
   * Create a conflict error
   */
  conflict(message: string, details?: Record<string, any>): BaseError;
  
  /**
   * Create a rate limit error
   */
  rateLimit(limit: number, window: string, retryAfter?: number): BaseError;
  
  /**
   * Create an external service error
   */
  externalService(
    serviceName: string, 
    message: string, 
    options?: {
      endpoint?: string;
      externalCode?: string;
      externalMessage?: string;
      retryable?: boolean;
      retryAfter?: number;
    }
  ): ExternalServiceError;
  
  /**
   * Create a network error
   */
  network(
    operation: 'connect' | 'read' | 'write' | 'timeout',
    message: string,
    options?: {
      host?: string;
      port?: number;
      retryable?: boolean;
    }
  ): NetworkError;
  
  /**
   * Create a configuration error
   */
  configuration(
    configKey: string,
    currentValue: any,
    expectedFormat: string,
    suggestion: string
  ): ConfigurationError;
  
  /**
   * Create an internal server error
   */
  internal(message: string, details?: Record<string, any>): BaseError;
}

/**
 * Error handler interface for consistent error processing
 */
export interface IErrorHandler {
  /**
   * Handle an error and return appropriate response
   */
  handle(error: Error | BaseError, context?: Record<string, any>): ErrorResponse;
  
  /**
   * Log an error with appropriate level
   */
  log(error: BaseError, context?: Record<string, any>): void;
  
  /**
   * Check if an error should be retried
   */
  isRetryable(error: BaseError): boolean;
  
  /**
   * Get retry delay for an error
   */
  getRetryDelay(error: BaseError, attempt: number): number;
}

/**
 * User-friendly error messages for common scenarios
 */
export const USER_FRIENDLY_MESSAGES = {
  DEVICE_NOT_FOUND: 'The requested device could not be found. It may have been removed or you may not have access to it.',
  DEVICE_OFFLINE: 'The device is currently offline. Please check the device connection and try again.',
  INVALID_PHONE_NUMBER: 'Please enter a valid phone number in international format (e.g., +1234567890).',
  MESSAGE_TOO_LONG: 'Your message is too long. Please keep it under 160 characters or split it into multiple messages.',
  RATE_LIMIT_EXCEEDED: 'You are sending messages too quickly. Please wait a moment before sending another message.',
  NETWORK_ERROR: 'Unable to connect to the service. Please check your internet connection and try again.',
  CONFIGURATION_ERROR: 'There is a configuration issue. Please contact your administrator.',
  VALIDATION_ERROR: 'Please check your input and correct any errors before submitting.',
  UNAUTHORIZED: 'You do not have permission to perform this action.',
  INTERNAL_ERROR: 'Something went wrong on our end. Please try again later or contact support if the problem persists.'
} as const;
