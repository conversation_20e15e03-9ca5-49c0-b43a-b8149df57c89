"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.USER_FRIENDLY_MESSAGES = exports.ErrorCategory = exports.ErrorSeverity = void 0;
var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["Low"] = "low";
    ErrorSeverity["Medium"] = "medium";
    ErrorSeverity["High"] = "high";
    ErrorSeverity["Critical"] = "critical";
})(ErrorSeverity || (exports.ErrorSeverity = ErrorSeverity = {}));
var ErrorCategory;
(function (ErrorCategory) {
    ErrorCategory["Validation"] = "validation";
    ErrorCategory["Authentication"] = "authentication";
    ErrorCategory["Authorization"] = "authorization";
    ErrorCategory["NotFound"] = "not_found";
    ErrorCategory["Conflict"] = "conflict";
    ErrorCategory["RateLimit"] = "rate_limit";
    ErrorCategory["External"] = "external";
    ErrorCategory["Internal"] = "internal";
    ErrorCategory["Network"] = "network";
    ErrorCategory["Timeout"] = "timeout";
    ErrorCategory["Configuration"] = "configuration";
})(ErrorCategory || (exports.ErrorCategory = ErrorCategory = {}));
exports.USER_FRIENDLY_MESSAGES = {
    DEVICE_NOT_FOUND: 'The requested device could not be found. It may have been removed or you may not have access to it.',
    DEVICE_OFFLINE: 'The device is currently offline. Please check the device connection and try again.',
    INVALID_PHONE_NUMBER: 'Please enter a valid phone number in international format (e.g., +1234567890).',
    MESSAGE_TOO_LONG: 'Your message is too long. Please keep it under 160 characters or split it into multiple messages.',
    RATE_LIMIT_EXCEEDED: 'You are sending messages too quickly. Please wait a moment before sending another message.',
    NETWORK_ERROR: 'Unable to connect to the service. Please check your internet connection and try again.',
    CONFIGURATION_ERROR: 'There is a configuration issue. Please contact your administrator.',
    VALIDATION_ERROR: 'Please check your input and correct any errors before submitting.',
    UNAUTHORIZED: 'You do not have permission to perform this action.',
    INTERNAL_ERROR: 'Something went wrong on our end. Please try again later or contact support if the problem persists.'
};
//# sourceMappingURL=index.js.map