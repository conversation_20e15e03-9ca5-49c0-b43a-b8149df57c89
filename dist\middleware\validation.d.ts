import { Request, Response, NextFunction } from 'express';
interface ValidationError {
    field: string;
    message: string;
    value?: any;
    rule?: string;
}
interface ValidationResult {
    valid: boolean;
    errors: ValidationError[];
}
declare abstract class BaseValidator {
    protected errors: ValidationError[];
    protected addError(field: string, message: string, value?: any, rule?: string): void;
    protected isValid(): boolean;
    protected getResult(): ValidationResult;
    protected reset(): void;
}
export declare const validatePhoneNumber: (phoneNumber: string) => boolean;
export declare const validateSMSMessage: (req: Request, _res: Response, next: NextFunction) => void;
export declare const validatePagination: (req: Request, _res: Response, next: NextFunction) => void;
export declare const validateMessageId: (req: Request, _res: Response, next: NextFunction) => void;
export declare const validateWebhookSignature: (secret: string) => (req: Request, _res: Response, next: NextFunction) => void;
export declare const validateLineSignature: (_channelSecret: string) => (req: Request, _res: Response, next: NextFunction) => void;
export declare class DeviceValidator extends BaseValidator {
    validateDeviceId(deviceId: any): ValidationResult;
    validateDeviceSettings(settings: any): ValidationResult;
}
export declare class ValidationMiddleware {
    private static deviceValidator;
    static validateDeviceId(): (req: Request, res: Response, next: NextFunction) => void;
    static validateDeviceSettings(): (req: Request, res: Response, next: NextFunction) => void;
    static validateDateRange(): (req: Request, res: Response, next: NextFunction) => void;
}
export {};
//# sourceMappingURL=validation.d.ts.map