"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogLevel = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor(level = 'info', filePath) {
        this.logLevel = this.parseLogLevel(level);
        if (filePath !== undefined) {
            this.logFilePath = filePath;
        }
        if (this.logFilePath) {
            this.ensureLogDirectory();
        }
    }
    parseLogLevel(level) {
        switch (level.toLowerCase()) {
            case 'error':
                return LogLevel.ERROR;
            case 'warn':
                return LogLevel.WARN;
            case 'info':
                return LogLevel.INFO;
            case 'debug':
                return LogLevel.DEBUG;
            default:
                return LogLevel.INFO;
        }
    }
    ensureLogDirectory() {
        if (!this.logFilePath)
            return;
        const logDir = path_1.default.dirname(this.logFilePath);
        if (!fs_1.default.existsSync(logDir)) {
            fs_1.default.mkdirSync(logDir, { recursive: true });
        }
    }
    formatMessage(level, message, meta) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level: level.toUpperCase(),
            message,
            ...(meta && { meta }),
        };
        return JSON.stringify(logEntry);
    }
    writeLog(level, levelName, message, meta) {
        if (level > this.logLevel)
            return;
        const formattedMessage = this.formatMessage(levelName, message, meta);
        console.log(formattedMessage);
        if (this.logFilePath) {
            fs_1.default.appendFileSync(this.logFilePath, formattedMessage + '\n');
        }
    }
    error(message, meta) {
        this.writeLog(LogLevel.ERROR, 'error', message, meta);
    }
    warn(message, meta) {
        this.writeLog(LogLevel.WARN, 'warn', message, meta);
    }
    info(message, meta) {
        this.writeLog(LogLevel.INFO, 'info', message, meta);
    }
    debug(message, meta) {
        this.writeLog(LogLevel.DEBUG, 'debug', message, meta);
    }
}
const logger = new Logger(process.env['LOG_LEVEL'] || 'info', process.env['LOG_FILE_PATH']);
exports.default = logger;
//# sourceMappingURL=logger.js.map