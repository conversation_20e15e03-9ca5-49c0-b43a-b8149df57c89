{"version": 3, "file": "errorFactory.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errorFactory.ts"], "names": [], "mappings": "AAKA,OAAO,EACL,SAAS,EACT,eAAe,EACf,oBAAoB,EACpB,YAAY,EACZ,kBAAkB,EAGlB,UAAU,EACV,YAAY,IAAI,aAAa,EAC9B,MAAM,UAAU,CAAC;AAKlB,qBAAa,YAAa,YAAW,aAAa;IAIhD,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,eAAe;IAiBtG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS;IAgBzE,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS;IAgBxE,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS;IAoB1D,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS;IAgBnE,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS;IAgBxE,eAAe,CACb,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,SAAS,CAAC,EAAE,OAAO,CAAC;QACpB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,GACA,oBAAoB;IAqBvB,OAAO,CACL,SAAS,EAAE,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,EACnD,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,SAAS,CAAC,EAAE,OAAO,CAAC;KACrB,GACA,YAAY;IA0Bf,aAAa,CACX,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,GAAG,EACjB,cAAc,EAAE,MAAM,EACtB,UAAU,EAAE,MAAM,GACjB,kBAAkB;IAmBrB,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS;CAYpE;AAGD,eAAO,MAAM,YAAY,cAAqB,CAAC;AAK/C,eAAO,MAAM,yBAAyB,GAAI,UAAU,MAAM,cACf,CAAC;AAE5C,eAAO,MAAM,6BAA6B,GAAI,aAAa,MAAM,oBAM5D,CAAC;AAEN,eAAO,MAAM,yBAAyB,GAAI,QAAQ,MAAM,EAAE,WAAW,MAAM,oBAMtE,CAAC;AAEN,eAAO,MAAM,wBAAwB,GAAI,UAAU,MAAM,yBAIrD,CAAC;AAEL,eAAO,MAAM,4BAA4B,GAAI,OAAO,MAAM,EAAE,eAAe,MAAM,cACF,CAAC;AAEhF,eAAO,MAAM,yBAAyB,GAAI,OAAO,MAAM,iBAC0B,CAAC;AAElF,eAAO,MAAM,+BAA+B,GAAI,WAAW,MAAM,uBAM9D,CAAC"}