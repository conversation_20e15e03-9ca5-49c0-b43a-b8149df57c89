/**
 * Comprehensive test examples for <PERSON>ceController
 * Demonstrates testing patterns for Express controllers with enhanced error handling
 */

import request from 'supertest';
import express from 'express';
import { DeviceController } from '../../src/controllers/deviceController';
import deviceService from '../../src/services/deviceService';
import { errorHandler } from '../../src/middleware/errorHandler';

// Mock the device service
jest.mock('../../src/services/deviceService');
const mockDeviceService = deviceService as jest.Mocked<typeof deviceService>;

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('DeviceController', () => {
  let app: express.Application;
  let deviceController: DeviceController;

  beforeEach(() => {
    // Create Express app for testing
    app = express();
    app.use(express.json());
    
    // Create controller instance
    deviceController = new DeviceController();
    
    // Setup routes
    app.get('/api/devices', deviceController.getDevices);
    app.get('/api/devices/:deviceId', deviceController.getDevice);
    app.delete('/api/devices/:deviceId', deviceController.deleteDevice);
    app.get('/api/devices/settings', deviceController.getSettings);
    app.put('/api/devices/settings', deviceController.updateSettings);
    app.patch('/api/devices/settings', deviceController.patchSettings);
    app.get('/api/devices/health', deviceController.getHealth);
    app.get('/api/devices/logs', deviceController.getLogs);
    app.post('/api/devices/:deviceId/export-inbox', deviceController.exportInbox);
    app.get('/api/devices/stats', deviceController.getDeviceStats);
    app.post('/api/devices/test-connection', deviceController.testConnection);
    
    // Add error handler
    app.use(errorHandler);
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('GET /api/devices', () => {
    const mockDevicesResponse = {
      success: true,
      data: [
        {
          id: 'device-1',
          name: 'Test Device 1',
          status: 'online',
          lastSeen: '2024-01-15T10:00:00.000Z',
          isConnected: true,
          messagesSentToday: 45,
          successRate: 98.5
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
        count: 1,
        offset: 0
      },
      timestamp: '2024-01-15T10:30:00.000Z',
      requestId: 'req_123'
    };

    it('should return devices list successfully', async () => {
      // Arrange
      mockDeviceService.getDevices.mockResolvedValue(mockDevicesResponse);

      // Act
      const response = await request(app)
        .get('/api/devices')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.timestamp).toBeDefined();
      expect(mockDeviceService.getDevices).toHaveBeenCalledTimes(1);
    });

    it('should handle query parameters correctly', async () => {
      // Arrange
      mockDeviceService.getDevices.mockResolvedValue(mockDevicesResponse);

      // Act
      const response = await request(app)
        .get('/api/devices')
        .query({
          page: '2',
          limit: '10',
          status: 'online',
          name: 'test',
          sortBy: 'name',
          sortOrder: 'asc'
        })
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(mockDeviceService.getDevices).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'test',
          status: 'online'
        }),
        expect.objectContaining({
          page: 2,
          limit: 10,
          sortBy: 'name',
          sortOrder: 'asc'
        })
      );
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      mockDeviceService.getDevices.mockRejectedValue(new Error('Service unavailable'));

      // Act
      const response = await request(app)
        .get('/api/devices')
        .expect(500);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('GET /api/devices/:deviceId', () => {
    const mockDeviceResponse = {
      success: true,
      data: {
        id: 'device-123',
        name: 'Test Device',
        status: 'online',
        lastSeen: '2024-01-15T10:00:00.000Z',
        connection: {
          isConnected: true,
          lastSeen: new Date('2024-01-15T10:00:00.000Z')
        },
        capabilities: {
          canSendSms: true,
          canReceiveSms: true,
          supportsDeliveryReports: true,
          maxMessageLength: 160,
          simSlots: [0, 1],
          features: ['sms', 'delivery_reports']
        },
        stats: {
          messagesSent: 1250,
          messagesReceived: 89,
          messagesSentToday: 45,
          successRate: 98.5,
          avgResponseTime: 1200,
          lastActivity: new Date('2024-01-15T10:00:00.000Z')
        }
      },
      timestamp: '2024-01-15T10:30:00.000Z',
      requestId: 'req_123'
    };

    it('should return device details successfully', async () => {
      // Arrange
      mockDeviceService.getDevice.mockResolvedValue(mockDeviceResponse);

      // Act
      const response = await request(app)
        .get('/api/devices/device-123')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe('device-123');
      expect(response.body.data.connection).toBeDefined();
      expect(response.body.data.capabilities).toBeDefined();
      expect(response.body.data.stats).toBeDefined();
      expect(mockDeviceService.getDevice).toHaveBeenCalledWith('device-123');
    });

    it('should return 404 when device not found', async () => {
      // Arrange
      const notFoundError = new Error("Device with ID 'non-existent' not found");
      (notFoundError as any).statusCode = 404;
      mockDeviceService.getDevice.mockRejectedValue(notFoundError);

      // Act
      const response = await request(app)
        .get('/api/devices/non-existent')
        .expect(404);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('DELETE /api/devices/:deviceId', () => {
    it('should delete device successfully', async () => {
      // Arrange
      mockDeviceService.deleteDevice.mockResolvedValue({
        success: true,
        metadata: { deviceId: 'device-123', timestamp: '2024-01-15T10:30:00.000Z' }
      });

      // Act
      const response = await request(app)
        .delete('/api/devices/device-123')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Device deleted successfully');
      expect(mockDeviceService.deleteDevice).toHaveBeenCalledWith('device-123');
    });
  });

  describe('GET /api/devices/settings', () => {
    const mockSettingsResponse = {
      success: true,
      data: {
        messages: {
          limitPeriod: 'PerHour',
          limitValue: 100,
          enableDeliveryReports: true
        },
        webhooks: {
          enabled: true,
          url: 'https://example.com/webhook'
        }
      },
      timestamp: '2024-01-15T10:30:00.000Z',
      requestId: 'req_123'
    };

    it('should return device settings successfully', async () => {
      // Arrange
      mockDeviceService.getSettings.mockResolvedValue(mockSettingsResponse);

      // Act
      const response = await request(app)
        .get('/api/devices/settings')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.messages).toBeDefined();
      expect(response.body.data.webhooks).toBeDefined();
    });
  });

  describe('PUT /api/devices/settings', () => {
    const mockSettings = {
      messages: {
        limitPeriod: 'PerHour',
        limitValue: 150,
        enableDeliveryReports: false
      }
    };

    it('should update settings successfully', async () => {
      // Arrange
      mockDeviceService.updateSettings.mockResolvedValue({
        success: true,
        metadata: { timestamp: '2024-01-15T10:30:00.000Z' }
      });

      // Act
      const response = await request(app)
        .put('/api/devices/settings')
        .send(mockSettings)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Device settings updated successfully');
      expect(mockDeviceService.updateSettings).toHaveBeenCalledWith(mockSettings);
    });

    it('should return 400 when settings data is missing', async () => {
      // Act
      const response = await request(app)
        .put('/api/devices/settings')
        .send({})
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Settings data is required');
    });
  });

  describe('POST /api/devices/test-connection', () => {
    it('should test connection successfully', async () => {
      // Arrange
      mockDeviceService.testConnection.mockResolvedValue({
        success: true,
        data: {
          connected: true,
          timestamp: '2024-01-15T10:30:00.000Z'
        },
        timestamp: '2024-01-15T10:30:00.000Z',
        requestId: 'req_123'
      });

      // Act
      const response = await request(app)
        .post('/api/devices/test-connection')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.connected).toBe(true);
      expect(response.body.message).toBe('Device connection successful');
    });

    it('should handle connection failure', async () => {
      // Arrange
      mockDeviceService.testConnection.mockResolvedValue({
        success: true,
        data: {
          connected: false,
          timestamp: '2024-01-15T10:30:00.000Z'
        },
        timestamp: '2024-01-15T10:30:00.000Z',
        requestId: 'req_123'
      });

      // Act
      const response = await request(app)
        .post('/api/devices/test-connection')
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data.connected).toBe(false);
      expect(response.body.message).toBe('Device connection failed');
    });
  });
});
