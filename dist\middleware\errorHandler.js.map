{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;;;;AACA,oCAA+F;AAE/F,6DAAqC;AAKrC,MAAa,QAAS,SAAQ,KAAK;IAWjC,YACE,OAAe,EACf,aAAqB,GAAG,EACxB,gBAAyB,IAAI,EAC7B,OAOC;QAED,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,IAAI,UAAU,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5F,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACvE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC;QAEpC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAMO,sBAAsB,CAAC,KAAa,EAAE,UAAkB;QAC9D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;YAC3D,OAAO,4CAA4C,CAAC;QACtD,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;YAC9D,OAAO,gDAAgD,CAAC;QAC1D,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;YAC3D,OAAO,qDAAqD,CAAC;QAC/D,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;YAC5D,OAAO,iDAAiD,CAAC;QAC3D,CAAC;QAED,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;YACtB,OAAO,0DAA0D,CAAC;QACpE,CAAC;QAED,OAAO,iDAAiD,CAAC;IAC3D,CAAC;IAMO,gBAAgB,CAAC,UAAkB;QACzC,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,UAAU,CAAC;QACxD,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,cAAc,CAAC;QAC5D,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,aAAa,CAAC;QAC3D,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,QAAQ,CAAC;QACtD,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,QAAQ,CAAC;QACtD,IAAI,UAAU,KAAK,GAAG;YAAE,OAAO,qBAAa,CAAC,SAAS,CAAC;QACvD,IAAI,UAAU,IAAI,GAAG;YAAE,OAAO,qBAAa,CAAC,QAAQ,CAAC;QACrD,OAAO,qBAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAMO,gBAAgB,CAAC,UAAkB;QACzC,IAAI,UAAU,IAAI,GAAG;YAAE,OAAO,qBAAa,CAAC,IAAI,CAAC;QACjD,IAAI,UAAU,IAAI,GAAG;YAAE,OAAO,qBAAa,CAAC,MAAM,CAAC;QACnD,OAAO,qBAAa,CAAC,GAAG,CAAC;IAC3B,CAAC;CACF;AA9FD,4BA8FC;AAKM,MAAM,YAAY,GAAG,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,YAAY,GAAG,uBAAuB,CAAC;IAC3C,IAAI,WAAW,GAAG,0DAA0D,CAAC;IAC7E,IAAI,QAAQ,GAAG,qBAAa,CAAC,QAAQ,CAAC;IACtC,IAAI,QAAQ,GAAG,qBAAa,CAAC,IAAI,CAAC;IAClC,IAAI,OAAwC,CAAC;IAG7C,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QAC7B,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;QAChC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC1B,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC1B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAC;QACjB,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QAC7B,WAAW,GAAG,iDAAiD,CAAC;QAChE,QAAQ,GAAG,qBAAa,CAAC,UAAU,CAAC;QACpC,QAAQ,GAAG,qBAAa,CAAC,MAAM,CAAC;IAClC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,YAAY,GAAG,cAAc,CAAC;QAC9B,WAAW,GAAG,gDAAgD,CAAC;QAC/D,QAAQ,GAAG,qBAAa,CAAC,cAAc,CAAC;QACxC,QAAQ,GAAG,qBAAa,CAAC,MAAM,CAAC;IAClC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACtC,UAAU,GAAG,GAAG,CAAC;QACjB,YAAY,GAAG,mBAAmB,CAAC;QACnC,WAAW,GAAG,+BAA+B,CAAC;QAC9C,QAAQ,GAAG,qBAAa,CAAC,UAAU,CAAC;QACpC,QAAQ,GAAG,qBAAa,CAAC,GAAG,CAAC;IAC/B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QACzE,UAAU,GAAG,GAAG,CAAC;QACjB,YAAY,GAAG,gBAAgB,CAAC;QAChC,WAAW,GAAG,oDAAoD,CAAC;QACnE,QAAQ,GAAG,qBAAa,CAAC,QAAQ,CAAC;QAClC,QAAQ,GAAG,qBAAa,CAAC,IAAI,CAAC;IAChC,CAAC;IAGD,MAAM,cAAc,GAAG;QACrB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW;QAChD,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW;QAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;IAGF,MAAM,OAAO,GAAG;QACd,KAAK,EAAE,YAAY;QACnB,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,OAAO,EAAE,cAAc;KACxB,CAAC;IAEF,IAAI,QAAQ,KAAK,qBAAa,CAAC,QAAQ,IAAI,QAAQ,KAAK,qBAAa,CAAC,IAAI,EAAE,CAAC;QAC3E,gBAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;SAAM,IAAI,QAAQ,KAAK,qBAAa,CAAC,MAAM,EAAE,CAAC;QAC7C,gBAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;SAAM,CAAC;QACN,gBAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAGD,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,IAAI,UAAU,EAAE;YACtB,OAAO,EAAE,YAAY;YACrB,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;SACtE;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,cAAc,CAAC,SAAS;KACpC,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAjGW,QAAA,YAAY,gBAiGvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;IACnE,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAE/E,gBAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;QAC7B,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS;KACV,CAAC,CAAC;IAEH,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAS,GAAG,CAAC,WAAW,YAAY;YAC7C,WAAW,EAAE,oDAAoD;YACjE,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,GAAG;YAC3B,UAAU,EAAE,GAAG;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS;SACV;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS;KACV,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtC,CAAC,CAAC;AA5BW,QAAA,eAAe,mBA4B1B;AAKK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAEzD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YACjC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QACnG,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YAEzD,IAAI,KAAK,YAAY,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACjD,KAAa,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC,CAAC;AAfW,QAAA,YAAY,gBAevB;AAKK,MAAM,sBAAsB,GAAG,CACpC,MAA8D,EACpD,EAAE;IACZ,MAAM,WAAW,GAA6B,EAAE,CAAC;IAEjD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,QAAQ,CACjB,mBAAmB,EACnB,GAAG,EACH,IAAI,EACJ;QACE,QAAQ,EAAE,qBAAa,CAAC,UAAU;QAClC,QAAQ,EAAE,qBAAa,CAAC,MAAM;QAC9B,OAAO,EAAE,EAAE,WAAW,EAAE;KACzB,CACF,CAAC;AACJ,CAAC,CAAC;AAtBW,QAAA,sBAAsB,0BAsBjC;AAKK,MAAM,qBAAqB,GAAG,CACnC,KAAa,EACb,QAAgB,EAChB,UAAmB,EACT,EAAE;IACZ,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;IAElD,OAAO,IAAI,QAAQ,CACjB,wBAAwB,KAAK,iBAAiB,aAAa,UAAU,EACrE,GAAG,EACH,IAAI,EACJ;QACE,QAAQ,EAAE,qBAAa,CAAC,SAAS;QACjC,QAAQ,EAAE,qBAAa,CAAC,MAAM;QAC9B,WAAW,EAAE,0EAA0E;QACvF,OAAO,EAAE;YACP,KAAK;YACL,QAAQ;YACR,UAAU,EAAE,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACrD;KACF,CACF,CAAC;AACJ,CAAC,CAAC;AAtBW,QAAA,qBAAqB,yBAsBhC"}