export declare enum ErrorSeverity {
    Low = "low",
    Medium = "medium",
    High = "high",
    Critical = "critical"
}
export declare enum ErrorCategory {
    Validation = "validation",
    Authentication = "authentication",
    Authorization = "authorization",
    NotFound = "not_found",
    Conflict = "conflict",
    RateLimit = "rate_limit",
    External = "external",
    Internal = "internal",
    Network = "network",
    Timeout = "timeout",
    Configuration = "configuration"
}
export interface BaseError {
    code: string;
    message: string;
    userMessage: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    statusCode: number;
    details?: Record<string, any>;
    timestamp: Date;
    requestId?: string;
    stack?: string;
}
export interface ValidationError extends BaseError {
    category: ErrorCategory.Validation;
    fieldErrors: FieldError[];
}
export interface FieldError {
    field: string;
    value: any;
    message: string;
    rule: string;
    context?: Record<string, any>;
}
export interface ExternalServiceError extends BaseError {
    category: ErrorCategory.External;
    serviceName: string;
    endpoint?: string;
    externalCode?: string;
    externalMessage?: string;
    retryable: boolean;
    retryAfter?: number;
}
export interface NetworkError extends BaseError {
    category: ErrorCategory.Network;
    operation: 'connect' | 'read' | 'write' | 'timeout';
    host?: string;
    port?: number;
    retryable: boolean;
}
export interface ConfigurationError extends BaseError {
    category: ErrorCategory.Configuration;
    configKey: string;
    currentValue: any;
    expectedFormat: string;
    suggestion: string;
}
export interface ErrorResponse {
    success: false;
    error: BaseError;
    timestamp: string;
    requestId?: string;
    context?: Record<string, any>;
}
export interface ErrorFactory {
    validation(message: string, fieldErrors: FieldError[], details?: Record<string, any>): ValidationError;
    authentication(message: string, details?: Record<string, any>): BaseError;
    authorization(message: string, details?: Record<string, any>): BaseError;
    notFound(resource: string, identifier?: string): BaseError;
    conflict(message: string, details?: Record<string, any>): BaseError;
    rateLimit(limit: number, window: string, retryAfter?: number): BaseError;
    externalService(serviceName: string, message: string, options?: {
        endpoint?: string;
        externalCode?: string;
        externalMessage?: string;
        retryable?: boolean;
        retryAfter?: number;
    }): ExternalServiceError;
    network(operation: 'connect' | 'read' | 'write' | 'timeout', message: string, options?: {
        host?: string;
        port?: number;
        retryable?: boolean;
    }): NetworkError;
    configuration(configKey: string, currentValue: any, expectedFormat: string, suggestion: string): ConfigurationError;
    internal(message: string, details?: Record<string, any>): BaseError;
}
export interface IErrorHandler {
    handle(error: Error | BaseError, context?: Record<string, any>): ErrorResponse;
    log(error: BaseError, context?: Record<string, any>): void;
    isRetryable(error: BaseError): boolean;
    getRetryDelay(error: BaseError, attempt: number): number;
}
export declare const USER_FRIENDLY_MESSAGES: {
    readonly DEVICE_NOT_FOUND: "The requested device could not be found. It may have been removed or you may not have access to it.";
    readonly DEVICE_OFFLINE: "The device is currently offline. Please check the device connection and try again.";
    readonly INVALID_PHONE_NUMBER: "Please enter a valid phone number in international format (e.g., +1234567890).";
    readonly MESSAGE_TOO_LONG: "Your message is too long. Please keep it under 160 characters or split it into multiple messages.";
    readonly RATE_LIMIT_EXCEEDED: "You are sending messages too quickly. Please wait a moment before sending another message.";
    readonly NETWORK_ERROR: "Unable to connect to the service. Please check your internet connection and try again.";
    readonly CONFIGURATION_ERROR: "There is a configuration issue. Please contact your administrator.";
    readonly VALIDATION_ERROR: "Please check your input and correct any errors before submitting.";
    readonly UNAUTHORIZED: "You do not have permission to perform this action.";
    readonly INTERNAL_ERROR: "Something went wrong on our end. Please try again later or contact support if the problem persists.";
};
//# sourceMappingURL=index.d.ts.map