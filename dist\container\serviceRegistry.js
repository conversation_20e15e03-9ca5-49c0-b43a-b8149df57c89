"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceFactory = void 0;
exports.registerServices = registerServices;
exports.initializeServices = initializeServices;
exports.performHealthCheck = performHealthCheck;
exports.shutdownServices = shutdownServices;
exports.getService = getService;
exports.createRequestScope = createRequestScope;
exports.createServiceMiddleware = createServiceMiddleware;
const DIContainer_1 = require("./DIContainer");
const serviceConfig_1 = require("../config/serviceConfig");
const deviceService_1 = __importDefault(require("../services/deviceService"));
const smsService_1 = __importDefault(require("../services/smsService"));
const lineService_1 = __importDefault(require("../services/lineService"));
const smsGatewayClient_1 = __importDefault(require("../services/smsGatewayClient"));
function registerServices() {
    registerHttpClients();
    registerCoreServices();
    registerBusinessServices();
    registerUtilityServices();
}
function registerHttpClients() {
    DIContainer_1.container.registerSingleton('smsGatewayClient', () => {
        const config = serviceConfig_1.configManager.getServiceConfig('smsGateway');
        return smsGatewayClient_1.default;
    });
}
function registerCoreServices() {
    DIContainer_1.container.register('serviceContext', (container) => {
        return {
            logger: container.resolve('logger'),
            config: serviceConfig_1.configManager.getServiceConfig('default')
        };
    }, DIContainer_1.ServiceLifetime.Transient);
    DIContainer_1.container.register('serviceContextFactory', () => {
        return (serviceName) => ({
            logger: DIContainer_1.container.resolve('logger'),
            config: serviceConfig_1.configManager.getServiceConfig(serviceName)
        });
    }, DIContainer_1.ServiceLifetime.Singleton);
}
function registerBusinessServices() {
    DIContainer_1.container.registerSingleton('deviceService', (container) => {
        const contextFactory = container.resolve('serviceContextFactory');
        const context = contextFactory('deviceService');
        const enhancedDeviceService = Object.create(deviceService_1.default);
        enhancedDeviceService.context = context;
        enhancedDeviceService.logger = context.logger;
        return enhancedDeviceService;
    });
    DIContainer_1.container.registerSingleton('smsService', (container) => {
        const contextFactory = container.resolve('serviceContextFactory');
        const context = contextFactory('smsService');
        const enhancedSmsService = Object.create(smsService_1.default);
        enhancedSmsService.context = context;
        enhancedSmsService.logger = context.logger;
        return enhancedSmsService;
    });
    DIContainer_1.container.registerSingleton('lineService', (container) => {
        const contextFactory = container.resolve('serviceContextFactory');
        const context = contextFactory('lineService');
        const enhancedLineService = Object.create(lineService_1.default);
        enhancedLineService.context = context;
        enhancedLineService.logger = context.logger;
        return enhancedLineService;
    });
}
function registerUtilityServices() {
    DIContainer_1.container.register('cacheService', () => {
        const cache = new Map();
        return {
            async get(key) {
                const item = cache.get(key);
                if (!item)
                    return null;
                if (Date.now() > item.expires) {
                    cache.delete(key);
                    return null;
                }
                return item.value;
            },
            async set(key, value, ttl = 300000) {
                cache.set(key, {
                    value,
                    expires: Date.now() + ttl
                });
            },
            async delete(key) {
                cache.delete(key);
            },
            async clear() {
                cache.clear();
            },
            async has(key) {
                const item = cache.get(key);
                if (!item)
                    return false;
                if (Date.now() > item.expires) {
                    cache.delete(key);
                    return false;
                }
                return true;
            }
        };
    }, DIContainer_1.ServiceLifetime.Singleton);
    DIContainer_1.container.register('metricsService', () => {
        const metrics = new Map();
        return {
            increment(metric, tags) {
                const key = this.buildKey(metric, tags);
                metrics.set(key, (metrics.get(key) || 0) + 1);
            },
            decrement(metric, tags) {
                const key = this.buildKey(metric, tags);
                metrics.set(key, (metrics.get(key) || 0) - 1);
            },
            gauge(metric, value, tags) {
                const key = this.buildKey(metric, tags);
                metrics.set(key, value);
            },
            timing(metric, duration, tags) {
                const key = this.buildKey(metric, tags);
                metrics.set(key, duration);
            },
            histogram(metric, value, tags) {
                const key = this.buildKey(metric, tags);
                metrics.set(key, value);
            },
            buildKey(metric, tags) {
                if (!tags)
                    return metric;
                const tagString = Object.entries(tags)
                    .map(([k, v]) => `${k}:${v}`)
                    .join(',');
                return `${metric}[${tagString}]`;
            },
            getMetrics() {
                return Object.fromEntries(metrics);
            }
        };
    }, DIContainer_1.ServiceLifetime.Singleton);
}
async function initializeServices() {
    try {
        await DIContainer_1.container.initializeServices();
        console.log('All services initialized successfully');
    }
    catch (error) {
        console.error('Failed to initialize services:', error);
        throw error;
    }
}
async function performHealthCheck() {
    return await DIContainer_1.container.healthCheck();
}
async function shutdownServices() {
    try {
        await DIContainer_1.container.dispose();
        console.log('All services shut down gracefully');
    }
    catch (error) {
        console.error('Error during service shutdown:', error);
        throw error;
    }
}
function getService(name) {
    return DIContainer_1.container.resolve(name);
}
function createRequestScope() {
    return DIContainer_1.container.createScope();
}
exports.ServiceFactory = {
    getDeviceService() {
        return DIContainer_1.container.resolve('deviceService');
    },
    getSmsService() {
        return DIContainer_1.container.resolve('smsService');
    },
    getLineService() {
        return DIContainer_1.container.resolve('lineService');
    },
    getCacheService() {
        return DIContainer_1.container.resolve('cacheService');
    },
    getMetricsService() {
        return DIContainer_1.container.resolve('metricsService');
    }
};
function createServiceMiddleware() {
    return (req, res, next) => {
        req.services = createRequestScope();
        res.on('finish', () => {
            req.services?.clearScoped();
        });
        next();
    };
}
//# sourceMappingURL=serviceRegistry.js.map