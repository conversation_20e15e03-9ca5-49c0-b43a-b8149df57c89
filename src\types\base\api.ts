/**
 * Base API types for request/response handling
 * Provides standardized interfaces for HTTP operations and API responses
 */

import { PaginationMeta, OperationResult } from './common';

// Generic API response wrapper optimized for UI consumption
export interface ApiResponse<T = any> {
  /** Whether the operation was successful */
  success: boolean;
  /** Response data if successful */
  data?: T;
  /** Error message if failed */
  error?: string;
  /** Additional message for the user */
  message?: string;
  /** Response timestamp */
  timestamp?: string;
  /** Request ID for tracking */
  requestId?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

// Paginated API response with enhanced metadata
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  /** Pagination metadata */
  pagination: PaginationMeta;
  /** Filters applied to the query */
  filters?: Record<string, any>;
  /** Sort information */
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
}

// Enhanced HTTP Client interface with better error handling
export interface HttpClient {
  /** GET request with optional headers */
  get<T>(url: string, headers?: Record<string, string>): Promise<T>;
  /** POST request with body and optional headers */
  post<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  /** PUT request with body and optional headers */
  put<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  /** PATCH request with body and optional headers */
  patch<T>(url: string, body: any, headers?: Record<string, string>): Promise<T>;
  /** DELETE request with optional headers */
  delete<T>(url: string, headers?: Record<string, string>): Promise<T>;
  /** Test connection to the service */
  testConnection?(): Promise<boolean>;
  /** Get base URL */
  getBaseUrl?(): string;
}

// Generic request/response types with validation support
export interface BaseRequest {
  /** Request timestamp */
  timestamp?: string;
  /** Request ID for tracking */
  requestId?: string;
  /** Additional request metadata */
  [key: string]: any;
}

export interface BaseResponse {
  /** Response timestamp */
  timestamp: string;
  /** Request ID for tracking */
  requestId?: string;
  /** Additional response data */
  [key: string]: any;
}

// Comprehensive error response types
export interface ErrorResponse {
  /** Error code */
  code?: string;
  /** Error message */
  error: string;
  /** Detailed error message */
  message?: string;
  /** Error details for debugging */
  details?: any;
  /** Field-specific validation errors */
  fieldErrors?: Record<string, string[]>;
  /** Timestamp when error occurred */
  timestamp: string;
  /** Request ID for tracking */
  requestId?: string;
  /** Stack trace (development only) */
  stack?: string;
}

// Health check types with comprehensive status information
export enum HealthStatus {
  Pass = 'pass',
  Warn = 'warn',
  Fail = 'fail'
}

export interface HealthCheck {
  /** Health check status */
  status: HealthStatus;
  /** Description of what is being checked */
  description: string;
  /** Observed value */
  observedValue: number;
  /** Unit of the observed value */
  observedUnit: string;
  /** Threshold values */
  threshold?: {
    min?: number;
    max?: number;
  };
  /** Last check timestamp */
  lastChecked: string;
  /** Additional check metadata */
  metadata?: Record<string, any>;
}

export interface HealthResponse {
  /** Overall system status */
  status: HealthStatus;
  /** Application version */
  version: string;
  /** Release ID */
  releaseId: number;
  /** Individual health checks */
  checks: Record<string, HealthCheck>;
  /** System uptime in seconds */
  uptime: number;
  /** Response timestamp */
  timestamp: string;
  /** Environment information */
  environment?: string;
}

// Request validation types
export interface ValidationError {
  /** Field name */
  field: string;
  /** Error message */
  message: string;
  /** Invalid value */
  value?: any;
  /** Validation rule that failed */
  rule?: string;
}

export interface ValidationResult {
  /** Whether validation passed */
  valid: boolean;
  /** Validation errors if any */
  errors: ValidationError[];
}

// API endpoint metadata for documentation and UI generation
export interface ApiEndpoint {
  /** HTTP method */
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  /** Endpoint path */
  path: string;
  /** Description */
  description: string;
  /** Request schema */
  requestSchema?: any;
  /** Response schema */
  responseSchema?: any;
  /** Whether authentication is required */
  requiresAuth?: boolean;
  /** Rate limit information */
  rateLimit?: {
    requests: number;
    window: string;
  };
}
