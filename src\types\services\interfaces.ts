/**
 * Service interfaces for dependency injection and better architecture
 * Provides contracts for all service implementations
 */

import {
  <PERSON><PERSON>,
  <PERSON>ceFilter,
  DeviceListItem,
  DeviceDetails,
  DeviceStats
} from '../device/device';
import {
  DeviceSettings,
  UpdateSettingsRequest
} from '../device/settings';
import {
  ApiResponse,
  PaginatedResponse,
  HealthResponse
} from '../base/api';
import { PaginationOptions, OperationResult } from '../base/common';

/**
 * Device service interface for managing SMS Gateway devices
 */
export interface IDeviceService {
  /**
   * Get all devices with optional filtering and pagination
   * @param filter - Optional filter criteria
   * @param pagination - Optional pagination options
   * @returns Promise resolving to paginated device list
   */
  getDevices(
    filter?: DeviceFilter, 
    pagination?: PaginationOptions
  ): Promise<PaginatedResponse<DeviceListItem>>;

  /**
   * Get a specific device by ID
   * @param deviceId - Device identifier
   * @returns Promise resolving to device details
   */
  getDevice(deviceId: string): Promise<ApiResponse<DeviceDetails>>;

  /**
   * Delete a device (soft delete)
   * @param deviceId - Device identifier
   * @returns Promise resolving to operation result
   */
  deleteDevice(deviceId: string): Promise<OperationResult>;

  /**
   * Get device settings
   * @param deviceId - Optional device ID for device-specific settings
   * @returns Promise resolving to device settings
   */
  getSettings(deviceId?: string): Promise<ApiResponse<DeviceSettings>>;

  /**
   * Update device settings (full update)
   * @param settings - Complete settings object
   * @param deviceId - Optional device ID
   * @returns Promise resolving to operation result
   */
  updateSettings(
    settings: DeviceSettings, 
    deviceId?: string
  ): Promise<OperationResult>;

  /**
   * Partially update device settings
   * @param settings - Partial settings object
   * @param deviceId - Optional device ID
   * @returns Promise resolving to operation result
   */
  patchSettings(
    settings: UpdateSettingsRequest,
    deviceId?: string
  ): Promise<OperationResult>;

  /**
   * Get system health information
   * @returns Promise resolving to health status
   */
  getHealth(): Promise<ApiResponse<HealthResponse>>;

  /**
   * Get system logs with optional date filtering
   * @param from - Start date for log filtering
   * @param to - End date for log filtering
   * @returns Promise resolving to log entries
   */
  getLogs(from?: Date, to?: Date): Promise<ApiResponse<any[]>>;

  /**
   * Export inbox messages for a device
   * @param deviceId - Device identifier
   * @param since - Start date for export
   * @param until - End date for export
   * @returns Promise resolving to operation result
   */
  exportInbox(deviceId: string, since: Date, until: Date): Promise<OperationResult>;

  /**
   * Get device statistics
   * @param deviceId - Optional device ID for specific device stats
   * @returns Promise resolving to device statistics
   */
  getDeviceStats(deviceId?: string): Promise<ApiResponse<DeviceStats>>;

  /**
   * Test connection to the SMS Gateway
   * @returns Promise resolving to connection status
   */
  testConnection(): Promise<ApiResponse<{ connected: boolean; timestamp: string }>>;
}

/**
 * Configuration interface for services
 */
export interface ServiceConfiguration {
  /** Service name */
  name: string;
  /** Service version */
  version: string;
  /** Whether the service is enabled */
  enabled: boolean;
  /** Service-specific options */
  options: Record<string, any>;
  /** Retry configuration */
  retry?: {
    attempts: number;
    delay: number;
    backoff: 'linear' | 'exponential';
  };
  /** Timeout configuration */
  timeout?: {
    connection: number;
    request: number;
  };
}

/**
 * Logger interface for consistent logging across services
 */
export interface ILogger {
  debug(message: string, meta?: Record<string, any>): void;
  info(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  error(message: string, meta?: Record<string, any>): void;
}

/**
 * Cache interface for service-level caching
 */
export interface ICache {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
}

/**
 * Metrics interface for service monitoring
 */
export interface IMetrics {
  increment(metric: string, tags?: Record<string, string>): void;
  decrement(metric: string, tags?: Record<string, string>): void;
  gauge(metric: string, value: number, tags?: Record<string, string>): void;
  timing(metric: string, duration: number, tags?: Record<string, string>): void;
  histogram(metric: string, value: number, tags?: Record<string, string>): void;
}

/**
 * Service context for dependency injection
 */
export interface ServiceContext {
  logger: ILogger;
  cache?: ICache;
  metrics?: IMetrics;
  config: ServiceConfiguration;
}

/**
 * Base service interface that all services should implement
 */
export interface IBaseService {
  /** Service name */
  readonly name: string;
  /** Service version */
  readonly version: string;
  /** Service context */
  readonly context: ServiceContext;
  
  /**
   * Initialize the service
   */
  initialize(): Promise<void>;
  
  /**
   * Cleanup service resources
   */
  cleanup(): Promise<void>;
  
  /**
   * Check if service is healthy
   */
  isHealthy(): Promise<boolean>;
}
