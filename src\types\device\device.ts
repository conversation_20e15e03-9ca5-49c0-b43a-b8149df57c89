/**
 * Device types and interfaces
 * Comprehensive device management types for SMS Gateway integration
 */

import { SoftDeletableEntity, BaseFilter, StatusInfo, DateRange } from '../base/common';

// Device status enumeration
export enum DeviceStatus {
  Online = 'online',
  Offline = 'offline',
  RecentlySeen = 'recently_seen',
  Deleted = 'deleted',
  Unknown = 'unknown',
}

// Device connection state
export interface DeviceConnectionInfo {
  /** Whether the device is currently connected */
  isConnected: boolean;
  /** Last seen timestamp */
  lastSeen: Date;
  /** Connection quality (0-100) */
  signalStrength?: number;
  /** Battery level (0-100) */
  batteryLevel?: number;
  /** Network type (WiFi, 4G, etc.) */
  networkType?: string;
  /** IP address */
  ipAddress?: string;
}

// Enhanced device entity with comprehensive information
export interface Device extends SoftDeletableEntity {
  /** Device name/identifier */
  name: string;
  /** Last seen timestamp as ISO string */
  lastSeen: string;
  /** Device connection information */
  connection?: DeviceConnectionInfo;
  /** Device status */
  status?: DeviceStatus;
  /** Device capabilities */
  capabilities?: DeviceCapabilities;
  /** Device statistics */
  stats?: DeviceStats;
  /** Device metadata */
  metadata?: Record<string, any>;
}

// Device capabilities
export interface DeviceCapabilities {
  /** Can send SMS */
  canSendSms: boolean;
  /** Can receive SMS */
  canReceiveSms: boolean;
  /** Supports delivery reports */
  supportsDeliveryReports: boolean;
  /** Maximum message length */
  maxMessageLength: number;
  /** Supported SIM slots */
  simSlots: number[];
  /** Supported features */
  features: string[];
}

// Device statistics for UI display
export interface DeviceStats {
  /** Total messages sent */
  messagesSent: number;
  /** Total messages received */
  messagesReceived: number;
  /** Messages sent today */
  messagesSentToday: number;
  /** Success rate (0-100) */
  successRate: number;
  /** Average response time in ms */
  avgResponseTime: number;
  /** Last activity timestamp */
  lastActivity: Date;
}

// Device creation data with validation
export interface CreateDeviceData {
  /** Device name (required) */
  name: string;
  /** Initial last seen timestamp */
  lastSeen?: string;
  /** Device capabilities */
  capabilities?: Partial<DeviceCapabilities>;
  /** Initial metadata */
  metadata?: Record<string, any>;
}

// Device update data with partial updates
export interface UpdateDeviceData {
  /** Updated device name */
  name?: string;
  /** Updated last seen timestamp */
  lastSeen?: string;
  /** Soft delete timestamp */
  deletedAt?: string | null;
  /** Updated connection info */
  connection?: Partial<DeviceConnectionInfo>;
  /** Updated capabilities */
  capabilities?: Partial<DeviceCapabilities>;
  /** Updated metadata */
  metadata?: Record<string, any>;
}

// Enhanced device filter with comprehensive options
export interface DeviceFilter extends BaseFilter {
  /** Filter by device name (partial match) */
  name?: string;
  /** Filter by active status (non-deleted devices) */
  active?: boolean;
  /** Filter by device status */
  status?: DeviceStatus | DeviceStatus[];
  /** Filter by last seen date range */
  lastSeenAfter?: Date;
  lastSeenBefore?: Date;
  /** Filter by connection status */
  isConnected?: boolean;
  /** Filter by capabilities */
  hasCapability?: string;
  /** Filter by SIM slot availability */
  hasSimSlot?: number;
}

// Device list response optimized for UI
export interface DeviceListItem {
  /** Device ID */
  id: string;
  /** Device name */
  name: string;
  /** Current status */
  status: DeviceStatus;
  /** Last seen timestamp */
  lastSeen: string;
  /** Connection status */
  isConnected: boolean;
  /** Battery level if available */
  batteryLevel?: number;
  /** Messages sent today */
  messagesSentToday: number;
  /** Success rate */
  successRate: number;
}

// Device details response for detailed view
export interface DeviceDetails extends Device {
  /** Recent activity log */
  recentActivity: DeviceActivity[];
  /** Performance metrics */
  performance: DevicePerformanceMetrics;
}

// Device activity log entry
export interface DeviceActivity {
  /** Activity ID */
  id: string;
  /** Activity type */
  type: 'message_sent' | 'message_received' | 'connection_change' | 'error' | 'status_update';
  /** Activity description */
  description: string;
  /** Activity timestamp */
  timestamp: Date;
  /** Related message ID if applicable */
  messageId?: string;
  /** Additional activity data */
  data?: Record<string, any>;
}

// Device performance metrics
export interface DevicePerformanceMetrics {
  /** Messages per hour */
  messagesPerHour: number;
  /** Average delivery time in seconds */
  avgDeliveryTime: number;
  /** Error rate (0-100) */
  errorRate: number;
  /** Uptime percentage (0-100) */
  uptime: number;
  /** Performance trend (improving, stable, declining) */
  trend: 'improving' | 'stable' | 'declining';
}
