"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimitErrorHandler = exports.validationErrorHandler = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.AppError = void 0;
const types_1 = require("../types");
const logger_1 = __importDefault(require("../utils/logger"));
class AppError extends Error {
    constructor(message, statusCode = 500, isOperational = true, options) {
        super(message);
        this.code = options?.code || `E${statusCode}`;
        this.message = message;
        this.userMessage = options?.userMessage || this.getUserFriendlyMessage(message, statusCode);
        this.category = options?.category || this.getErrorCategory(statusCode);
        this.severity = options?.severity || this.getErrorSeverity(statusCode);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.details = options?.details;
        this.timestamp = new Date();
        this.requestId = options?.requestId;
        Error.captureStackTrace(this, this.constructor);
    }
    getUserFriendlyMessage(error, statusCode) {
        const errorLower = error.toLowerCase();
        if (errorLower.includes('not found') || statusCode === 404) {
            return 'The requested resource could not be found.';
        }
        if (errorLower.includes('unauthorized') || statusCode === 401) {
            return 'You are not authorized to perform this action.';
        }
        if (errorLower.includes('forbidden') || statusCode === 403) {
            return 'You do not have permission to access this resource.';
        }
        if (errorLower.includes('validation') || statusCode === 400) {
            return 'Please check your input and correct any errors.';
        }
        if (statusCode >= 500) {
            return 'Something went wrong on our end. Please try again later.';
        }
        return 'An unexpected error occurred. Please try again.';
    }
    getErrorCategory(statusCode) {
        if (statusCode === 400)
            return types_1.ErrorCategory.Validation;
        if (statusCode === 401)
            return types_1.ErrorCategory.Authentication;
        if (statusCode === 403)
            return types_1.ErrorCategory.Authorization;
        if (statusCode === 404)
            return types_1.ErrorCategory.NotFound;
        if (statusCode === 409)
            return types_1.ErrorCategory.Conflict;
        if (statusCode === 429)
            return types_1.ErrorCategory.RateLimit;
        if (statusCode >= 500)
            return types_1.ErrorCategory.Internal;
        return types_1.ErrorCategory.Internal;
    }
    getErrorSeverity(statusCode) {
        if (statusCode >= 500)
            return types_1.ErrorSeverity.High;
        if (statusCode >= 400)
            return types_1.ErrorSeverity.Medium;
        return types_1.ErrorSeverity.Low;
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, _next) => {
    let statusCode = 500;
    let errorMessage = 'Internal Server Error';
    let userMessage = 'Something went wrong on our end. Please try again later.';
    let category = types_1.ErrorCategory.Internal;
    let severity = types_1.ErrorSeverity.High;
    let details;
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        errorMessage = error.message;
        userMessage = error.userMessage;
        category = error.category;
        severity = error.severity;
        details = error.details;
    }
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        errorMessage = error.message;
        userMessage = 'Please check your input and correct any errors.';
        category = types_1.ErrorCategory.Validation;
        severity = types_1.ErrorSeverity.Medium;
    }
    else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        errorMessage = 'Unauthorized';
        userMessage = 'You are not authorized to perform this action.';
        category = types_1.ErrorCategory.Authentication;
        severity = types_1.ErrorSeverity.Medium;
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        errorMessage = 'Invalid ID format';
        userMessage = 'The provided ID is not valid.';
        category = types_1.ErrorCategory.Validation;
        severity = types_1.ErrorSeverity.Low;
    }
    else if (error.name === 'MongoError' || error.name === 'MongooseError') {
        statusCode = 500;
        errorMessage = 'Database error';
        userMessage = 'A database error occurred. Please try again later.';
        category = types_1.ErrorCategory.Internal;
        severity = types_1.ErrorSeverity.High;
    }
    const requestContext = {
        requestId: req.headers['x-request-id'],
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        userId: req.headers['x-user-id'],
        timestamp: new Date().toISOString()
    };
    const logData = {
        error: errorMessage,
        statusCode,
        category,
        severity,
        details,
        stack: error.stack,
        request: requestContext
    };
    if (severity === types_1.ErrorSeverity.Critical || severity === types_1.ErrorSeverity.High) {
        logger_1.default.error('Application error occurred', logData);
    }
    else if (severity === types_1.ErrorSeverity.Medium) {
        logger_1.default.warn('Application warning', logData);
    }
    else {
        logger_1.default.info('Application info', logData);
    }
    const errorResponse = {
        success: false,
        error: {
            code: `E${statusCode}`,
            message: errorMessage,
            userMessage,
            category,
            severity,
            statusCode,
            details,
            timestamp: new Date(),
            requestId: requestContext.requestId,
            ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
        },
        timestamp: new Date().toISOString(),
        requestId: requestContext.requestId
    };
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    const requestId = req.headers['x-request-id'] || `req_${Date.now()}`;
    logger_1.default.warn('Route not found', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId
    });
    const errorResponse = {
        success: false,
        error: {
            code: 'E404',
            message: `Route ${req.originalUrl} not found`,
            userMessage: 'The requested page or resource could not be found.',
            category: types_1.ErrorCategory.NotFound,
            severity: types_1.ErrorSeverity.Low,
            statusCode: 404,
            timestamp: new Date(),
            requestId
        },
        timestamp: new Date().toISOString(),
        requestId
    };
    res.status(404).json(errorResponse);
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        if (!req.headers['x-request-id']) {
            req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        }
        Promise.resolve(fn(req, res, next)).catch((error) => {
            if (error instanceof AppError && !error.requestId) {
                error.requestId = req.headers['x-request-id'];
            }
            next(error);
        });
    };
};
exports.asyncHandler = asyncHandler;
const validationErrorHandler = (errors) => {
    const fieldErrors = {};
    errors.forEach(error => {
        if (!fieldErrors[error.field]) {
            fieldErrors[error.field] = [];
        }
        fieldErrors[error.field].push(error.message);
    });
    return new AppError('Validation failed', 400, true, {
        category: types_1.ErrorCategory.Validation,
        severity: types_1.ErrorSeverity.Medium,
        details: { fieldErrors }
    });
};
exports.validationErrorHandler = validationErrorHandler;
const rateLimitErrorHandler = (limit, windowMs, retryAfter) => {
    const windowMinutes = Math.ceil(windowMs / 60000);
    return new AppError(`Rate limit exceeded: ${limit} requests per ${windowMinutes} minutes`, 429, true, {
        category: types_1.ErrorCategory.RateLimit,
        severity: types_1.ErrorSeverity.Medium,
        userMessage: 'You are making requests too quickly. Please wait a moment and try again.',
        details: {
            limit,
            windowMs,
            retryAfter: retryAfter || Math.ceil(windowMs / 1000)
        }
    });
};
exports.rateLimitErrorHandler = rateLimitErrorHandler;
//# sourceMappingURL=errorHandler.js.map