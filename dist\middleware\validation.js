"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationMiddleware = exports.DeviceValidator = exports.validateLineSignature = exports.validateWebhookSignature = exports.validateMessageId = exports.validatePagination = exports.validateSMSMessage = exports.validatePhoneNumber = void 0;
const errorHandler_1 = require("./errorHandler");
class BaseValidator {
    constructor() {
        this.errors = [];
    }
    addError(field, message, value, rule) {
        this.errors.push({ field, message, value, rule });
    }
    isValid() {
        return this.errors.length === 0;
    }
    getResult() {
        return {
            valid: this.isValid(),
            errors: [...this.errors]
        };
    }
    reset() {
        this.errors = [];
    }
}
const validatePhoneNumber = (phoneNumber) => {
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
};
exports.validatePhoneNumber = validatePhoneNumber;
const validateSMSMessage = (req, _res, next) => {
    const { phoneNumbers, message } = req.body;
    if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
        throw new errorHandler_1.AppError('Phone numbers are required and must be a non-empty array', 400);
    }
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
        throw new errorHandler_1.AppError('Message is required and must be a non-empty string', 400);
    }
    if (message.length > 1600) {
        throw new errorHandler_1.AppError('Message is too long (maximum 1600 characters)', 400);
    }
    if (phoneNumbers.length > 10) {
        throw new errorHandler_1.AppError('Too many phone numbers (maximum 10 per request)', 400);
    }
    const invalidNumbers = phoneNumbers.filter((num) => !(0, exports.validatePhoneNumber)(num));
    if (invalidNumbers.length > 0) {
        throw new errorHandler_1.AppError(`Invalid phone numbers: ${invalidNumbers.join(', ')}`, 400);
    }
    next();
};
exports.validateSMSMessage = validateSMSMessage;
const validatePagination = (req, _res, next) => {
    const { page, limit } = req.query;
    if (page && (isNaN(Number(page)) || Number(page) < 1)) {
        throw new errorHandler_1.AppError('Page must be a positive integer', 400);
    }
    if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
        throw new errorHandler_1.AppError('Limit must be a positive integer between 1 and 100', 400);
    }
    next();
};
exports.validatePagination = validatePagination;
const validateMessageId = (req, _res, next) => {
    const { messageId } = req.params;
    if (!messageId || typeof messageId !== 'string' || messageId.trim().length === 0) {
        throw new errorHandler_1.AppError('Message ID is required', 400);
    }
    next();
};
exports.validateMessageId = validateMessageId;
const validateWebhookSignature = (secret) => {
    return (req, _res, next) => {
        const signature = req.headers['x-webhook-signature'];
        if (!signature) {
            throw new errorHandler_1.AppError('Webhook signature is required', 401);
        }
        if (signature !== secret) {
            throw new errorHandler_1.AppError('Invalid webhook signature', 401);
        }
        next();
    };
};
exports.validateWebhookSignature = validateWebhookSignature;
const validateLineSignature = (_channelSecret) => {
    return (req, _res, next) => {
        const signature = req.headers['x-line-signature'];
        if (!signature) {
            throw new errorHandler_1.AppError('LINE signature is required', 401);
        }
        next();
    };
};
exports.validateLineSignature = validateLineSignature;
class DeviceValidator extends BaseValidator {
    validateDeviceId(deviceId) {
        this.reset();
        if (!deviceId) {
            this.addError('deviceId', 'Device ID is required', deviceId, 'required');
        }
        else if (typeof deviceId !== 'string') {
            this.addError('deviceId', 'Device ID must be a string', deviceId, 'type');
        }
        else if (deviceId.trim().length === 0) {
            this.addError('deviceId', 'Device ID cannot be empty', deviceId, 'empty');
        }
        else if (deviceId.length > 100) {
            this.addError('deviceId', 'Device ID cannot exceed 100 characters', deviceId, 'maxLength');
        }
        return this.getResult();
    }
    validateDeviceSettings(settings) {
        this.reset();
        if (!settings || typeof settings !== 'object') {
            this.addError('settings', 'Settings must be an object', settings, 'type');
            return this.getResult();
        }
        if (settings.messages && typeof settings.messages !== 'object') {
            this.addError('messages', 'Messages settings must be an object', settings.messages, 'type');
        }
        if (settings.webhooks) {
            if (typeof settings.webhooks !== 'object') {
                this.addError('webhooks', 'Webhooks settings must be an object', settings.webhooks, 'type');
            }
            else if (settings.webhooks.url && typeof settings.webhooks.url !== 'string') {
                this.addError('webhooks.url', 'Webhook URL must be a string', settings.webhooks.url, 'type');
            }
        }
        return this.getResult();
    }
}
exports.DeviceValidator = DeviceValidator;
class ValidationMiddleware {
    static validateDeviceId() {
        return (req, res, next) => {
            const { deviceId } = req.params;
            const result = ValidationMiddleware.deviceValidator.validateDeviceId(deviceId);
            if (!result.valid) {
                const error = (0, errorHandler_1.validationErrorHandler)(result.errors);
                return next(error);
            }
            next();
        };
    }
    static validateDeviceSettings() {
        return (req, res, next) => {
            const result = ValidationMiddleware.deviceValidator.validateDeviceSettings(req.body);
            if (!result.valid) {
                const error = (0, errorHandler_1.validationErrorHandler)(result.errors);
                return next(error);
            }
            next();
        };
    }
    static validateDateRange() {
        return (req, res, next) => {
            const { since, until } = req.body;
            const errors = [];
            if (!since) {
                errors.push({ field: 'since', message: 'Since date is required', value: since, rule: 'required' });
            }
            else {
                const sinceDate = new Date(since);
                if (isNaN(sinceDate.getTime())) {
                    errors.push({ field: 'since', message: 'Invalid since date format', value: since, rule: 'date' });
                }
            }
            if (!until) {
                errors.push({ field: 'until', message: 'Until date is required', value: until, rule: 'required' });
            }
            else {
                const untilDate = new Date(until);
                if (isNaN(untilDate.getTime())) {
                    errors.push({ field: 'until', message: 'Invalid until date format', value: until, rule: 'date' });
                }
            }
            if (errors.length === 0 && since && until) {
                const sinceDate = new Date(since);
                const untilDate = new Date(until);
                if (sinceDate >= untilDate) {
                    errors.push({ field: 'dateRange', message: 'Since date must be before until date', rule: 'dateRange' });
                }
            }
            if (errors.length > 0) {
                const error = (0, errorHandler_1.validationErrorHandler)(errors);
                return next(error);
            }
            next();
        };
    }
}
exports.ValidationMiddleware = ValidationMiddleware;
ValidationMiddleware.deviceValidator = new DeviceValidator();
//# sourceMappingURL=validation.js.map