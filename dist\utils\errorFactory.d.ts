import { BaseError, ValidationError, ExternalServiceError, NetworkError, ConfigurationError, FieldError, ErrorFactory as IErrorFactory } from '../types';
export declare class ErrorFactory implements IErrorFactory {
    validation(message: string, fieldErrors: FieldError[], details?: Record<string, any>): ValidationError;
    authentication(message: string, details?: Record<string, any>): BaseError;
    authorization(message: string, details?: Record<string, any>): BaseError;
    notFound(resource: string, identifier?: string): BaseError;
    conflict(message: string, details?: Record<string, any>): BaseError;
    rateLimit(limit: number, window: string, retryAfter?: number): BaseError;
    externalService(serviceName: string, message: string, options?: {
        endpoint?: string;
        externalCode?: string;
        externalMessage?: string;
        retryable?: boolean;
        retryAfter?: number;
    }): ExternalServiceError;
    network(operation: 'connect' | 'read' | 'write' | 'timeout', message: string, options?: {
        host?: string;
        port?: number;
        retryable?: boolean;
    }): NetworkError;
    configuration(configKey: string, currentValue: any, expectedFormat: string, suggestion: string): ConfigurationError;
    internal(message: string, details?: Record<string, any>): BaseError;
}
export declare const errorFactory: ErrorFactory;
export declare const createDeviceNotFoundError: (deviceId: string) => BaseError;
export declare const createInvalidPhoneNumberError: (phoneNumber: string) => ValidationError;
export declare const createMessageTooLongError: (length: number, maxLength: number) => ValidationError;
export declare const createDeviceOfflineError: (deviceId: string) => ExternalServiceError;
export declare const createRateLimitExceededError: (limit: number, windowMinutes: number) => BaseError;
export declare const createNetworkTimeoutError: (host?: string) => NetworkError;
export declare const createConfigurationMissingError: (configKey: string) => ConfigurationError;
//# sourceMappingURL=errorFactory.d.ts.map