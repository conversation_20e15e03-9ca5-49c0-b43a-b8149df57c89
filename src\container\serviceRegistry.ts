/**
 * Service Registry
 * Registers all application services with the DI container
 */

import { container, ServiceLifetime } from './DIContainer';
import { configManager } from '../config/serviceConfig';
import { IDeviceService, ServiceContext } from '../types';

// Import services
import deviceService from '../services/deviceService';
import smsService from '../services/smsService';
import lineService from '../services/lineService';
import smsGatewayClient from '../services/smsGatewayClient';

/**
 * Register all application services
 */
export function registerServices(): void {
  // Register HTTP clients and external service adapters
  registerHttpClients();
  
  // Register core services
  registerCoreServices();
  
  // Register business services
  registerBusinessServices();
  
  // Register utility services
  registerUtilityServices();
}

/**
 * Register HTTP clients and external service adapters
 */
function registerHttpClients(): void {
  // SMS Gateway Client
  container.registerSingleton('smsGatewayClient', () => {
    const config = configManager.getServiceConfig('smsGateway');
    return smsGatewayClient;
  });
}

/**
 * Register core services
 */
function registerCoreServices(): void {
  // Service Context Factory
  container.register<ServiceContext>('serviceContext', (container) => {
    return {
      logger: container.resolve('logger'),
      config: configManager.getServiceConfig('default')
    };
  }, ServiceLifetime.Transient);

  // Enhanced Service Context Factory with specific service config
  container.register<(serviceName: string) => ServiceContext>('serviceContextFactory', () => {
    return (serviceName: string) => ({
      logger: container.resolve('logger'),
      config: configManager.getServiceConfig(serviceName)
    });
  }, ServiceLifetime.Singleton);
}

/**
 * Register business services
 */
function registerBusinessServices(): void {
  // Device Service
  container.registerSingleton<IDeviceService>('deviceService', (container) => {
    const contextFactory = container.resolve<(serviceName: string) => ServiceContext>('serviceContextFactory');
    const context = contextFactory('deviceService');
    
    // Create enhanced device service with DI context
    const enhancedDeviceService = Object.create(deviceService);
    enhancedDeviceService.context = context;
    enhancedDeviceService.logger = context.logger;
    
    return enhancedDeviceService;
  });

  // SMS Service
  container.registerSingleton('smsService', (container) => {
    const contextFactory = container.resolve<(serviceName: string) => ServiceContext>('serviceContextFactory');
    const context = contextFactory('smsService');
    
    const enhancedSmsService = Object.create(smsService);
    enhancedSmsService.context = context;
    enhancedSmsService.logger = context.logger;
    
    return enhancedSmsService;
  });

  // LINE Service
  container.registerSingleton('lineService', (container) => {
    const contextFactory = container.resolve<(serviceName: string) => ServiceContext>('serviceContextFactory');
    const context = contextFactory('lineService');
    
    const enhancedLineService = Object.create(lineService);
    enhancedLineService.context = context;
    enhancedLineService.logger = context.logger;
    
    return enhancedLineService;
  });
}

/**
 * Register utility services
 */
function registerUtilityServices(): void {
  // Cache Service (if implemented)
  container.register('cacheService', () => {
    // Simple in-memory cache implementation
    const cache = new Map<string, { value: any; expires: number }>();
    
    return {
      async get<T>(key: string): Promise<T | null> {
        const item = cache.get(key);
        if (!item) return null;
        
        if (Date.now() > item.expires) {
          cache.delete(key);
          return null;
        }
        
        return item.value;
      },
      
      async set<T>(key: string, value: T, ttl: number = 300000): Promise<void> {
        cache.set(key, {
          value,
          expires: Date.now() + ttl
        });
      },
      
      async delete(key: string): Promise<void> {
        cache.delete(key);
      },
      
      async clear(): Promise<void> {
        cache.clear();
      },
      
      async has(key: string): Promise<boolean> {
        const item = cache.get(key);
        if (!item) return false;
        
        if (Date.now() > item.expires) {
          cache.delete(key);
          return false;
        }
        
        return true;
      }
    };
  }, ServiceLifetime.Singleton);

  // Metrics Service (basic implementation)
  container.register('metricsService', () => {
    const metrics = new Map<string, number>();
    
    return {
      increment(metric: string, tags?: Record<string, string>): void {
        const key = this.buildKey(metric, tags);
        metrics.set(key, (metrics.get(key) || 0) + 1);
      },
      
      decrement(metric: string, tags?: Record<string, string>): void {
        const key = this.buildKey(metric, tags);
        metrics.set(key, (metrics.get(key) || 0) - 1);
      },
      
      gauge(metric: string, value: number, tags?: Record<string, string>): void {
        const key = this.buildKey(metric, tags);
        metrics.set(key, value);
      },
      
      timing(metric: string, duration: number, tags?: Record<string, string>): void {
        const key = this.buildKey(metric, tags);
        metrics.set(key, duration);
      },
      
      histogram(metric: string, value: number, tags?: Record<string, string>): void {
        const key = this.buildKey(metric, tags);
        metrics.set(key, value);
      },
      
      buildKey(metric: string, tags?: Record<string, string>): string {
        if (!tags) return metric;
        const tagString = Object.entries(tags)
          .map(([k, v]) => `${k}:${v}`)
          .join(',');
        return `${metric}[${tagString}]`;
      },
      
      getMetrics(): Record<string, number> {
        return Object.fromEntries(metrics);
      }
    };
  }, ServiceLifetime.Singleton);
}

/**
 * Initialize all services
 */
export async function initializeServices(): Promise<void> {
  try {
    await container.initializeServices();
    console.log('All services initialized successfully');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    throw error;
  }
}

/**
 * Perform health check on all services
 */
export async function performHealthCheck(): Promise<{ healthy: boolean; services: Record<string, boolean> }> {
  return await container.healthCheck();
}

/**
 * Graceful shutdown of all services
 */
export async function shutdownServices(): Promise<void> {
  try {
    await container.dispose();
    console.log('All services shut down gracefully');
  } catch (error) {
    console.error('Error during service shutdown:', error);
    throw error;
  }
}

/**
 * Get service instance by name
 */
export function getService<T>(name: string): T {
  return container.resolve<T>(name);
}

/**
 * Create request-scoped container
 */
export function createRequestScope() {
  return container.createScope();
}

/**
 * Service factory functions for easy access
 */
export const ServiceFactory = {
  getDeviceService(): IDeviceService {
    return container.resolve<IDeviceService>('deviceService');
  },
  
  getSmsService() {
    return container.resolve('smsService');
  },
  
  getLineService() {
    return container.resolve('lineService');
  },
  
  getCacheService() {
    return container.resolve('cacheService');
  },
  
  getMetricsService() {
    return container.resolve('metricsService');
  }
};

/**
 * Middleware factory for request-scoped services
 */
export function createServiceMiddleware() {
  return (req: any, res: any, next: any) => {
    // Create request-scoped container
    req.services = createRequestScope();
    
    // Add cleanup on response finish
    res.on('finish', () => {
      req.services?.clearScoped();
    });
    
    next();
  };
}
