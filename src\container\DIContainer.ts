/**
 * Dependency Injection Container
 * Provides service registration, resolution, and lifecycle management
 */

import { ILogger, ICache, IMetrics, ServiceContext, IBaseService } from '../types';
import { configManager } from '../config/serviceConfig';
import logger from '../utils/logger';

/**
 * Service lifecycle types
 */
export enum ServiceLifetime {
  Singleton = 'singleton',
  Transient = 'transient',
  Scoped = 'scoped'
}

/**
 * Service registration descriptor
 */
export interface ServiceDescriptor<T = any> {
  name: string;
  factory: (container: DIContainer) => T;
  lifetime: ServiceLifetime;
  dependencies?: string[];
}

/**
 * Dependency Injection Container implementation
 */
export class DIContainer {
  private static instance: DIContainer;
  private services = new Map<string, ServiceDescriptor>();
  private singletons = new Map<string, any>();
  private scoped = new Map<string, any>();

  private constructor() {
    this.registerCoreServices();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  /**
   * Register core services
   */
  private registerCoreServices(): void {
    // Register logger
    this.register<ILogger>('logger', () => logger, ServiceLifetime.Singleton);

    // Register configuration manager
    this.register('configManager', () => configManager, ServiceLifetime.Singleton);

    // Register service context factory
    this.register<ServiceContext>('serviceContext', (container) => ({
      logger: container.resolve<ILogger>('logger'),
      config: configManager.getServiceConfig('default')
    }), ServiceLifetime.Transient);
  }

  /**
   * Register a service
   */
  register<T>(
    name: string,
    factory: (container: DIContainer) => T,
    lifetime: ServiceLifetime = ServiceLifetime.Transient,
    dependencies: string[] = []
  ): void {
    this.services.set(name, {
      name,
      factory,
      lifetime,
      dependencies
    });
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: (container: DIContainer) => T,
    dependencies: string[] = []
  ): void {
    this.register(name, factory, ServiceLifetime.Singleton, dependencies);
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    name: string,
    factory: (container: DIContainer) => T,
    dependencies: string[] = []
  ): void {
    this.register(name, factory, ServiceLifetime.Transient, dependencies);
  }

  /**
   * Register a scoped service
   */
  registerScoped<T>(
    name: string,
    factory: (container: DIContainer) => T,
    dependencies: string[] = []
  ): void {
    this.register(name, factory, ServiceLifetime.Scoped, dependencies);
  }

  /**
   * Resolve a service by name
   */
  resolve<T>(name: string): T {
    const descriptor = this.services.get(name);
    
    if (!descriptor) {
      throw new Error(`Service '${name}' is not registered`);
    }

    // Check for circular dependencies
    this.checkCircularDependencies(name, new Set());

    switch (descriptor.lifetime) {
      case ServiceLifetime.Singleton:
        return this.resolveSingleton<T>(descriptor);
      
      case ServiceLifetime.Scoped:
        return this.resolveScoped<T>(descriptor);
      
      case ServiceLifetime.Transient:
      default:
        return this.resolveTransient<T>(descriptor);
    }
  }

  /**
   * Resolve singleton service
   */
  private resolveSingleton<T>(descriptor: ServiceDescriptor<T>): T {
    if (this.singletons.has(descriptor.name)) {
      return this.singletons.get(descriptor.name);
    }

    const instance = descriptor.factory(this);
    this.singletons.set(descriptor.name, instance);
    return instance;
  }

  /**
   * Resolve scoped service
   */
  private resolveScoped<T>(descriptor: ServiceDescriptor<T>): T {
    if (this.scoped.has(descriptor.name)) {
      return this.scoped.get(descriptor.name);
    }

    const instance = descriptor.factory(this);
    this.scoped.set(descriptor.name, instance);
    return instance;
  }

  /**
   * Resolve transient service
   */
  private resolveTransient<T>(descriptor: ServiceDescriptor<T>): T {
    return descriptor.factory(this);
  }

  /**
   * Check for circular dependencies
   */
  private checkCircularDependencies(serviceName: string, visited: Set<string>): void {
    if (visited.has(serviceName)) {
      throw new Error(`Circular dependency detected: ${Array.from(visited).join(' -> ')} -> ${serviceName}`);
    }

    visited.add(serviceName);

    const descriptor = this.services.get(serviceName);
    if (descriptor?.dependencies) {
      for (const dependency of descriptor.dependencies) {
        this.checkCircularDependencies(dependency, new Set(visited));
      }
    }
  }

  /**
   * Check if service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clear scoped services (useful for request-scoped services)
   */
  clearScoped(): void {
    this.scoped.clear();
  }

  /**
   * Dispose of all services that implement IDisposable
   */
  async dispose(): Promise<void> {
    // Dispose singletons
    for (const [name, instance] of this.singletons) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          await instance.dispose();
        } catch (error) {
          logger.error(`Error disposing singleton service '${name}'`, { error });
        }
      }
    }

    // Dispose scoped services
    for (const [name, instance] of this.scoped) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          await instance.dispose();
        } catch (error) {
          logger.error(`Error disposing scoped service '${name}'`, { error });
        }
      }
    }

    this.singletons.clear();
    this.scoped.clear();
  }

  /**
   * Create a child container for scoped services
   */
  createScope(): DIContainer {
    const childContainer = Object.create(this);
    childContainer.scoped = new Map();
    return childContainer;
  }

  /**
   * Initialize all registered services that implement IBaseService
   */
  async initializeServices(): Promise<void> {
    const serviceNames = this.getRegisteredServices();
    
    for (const serviceName of serviceNames) {
      try {
        const service = this.resolve(serviceName);
        
        if (service && typeof service.initialize === 'function') {
          await service.initialize();
          logger.info(`Service '${serviceName}' initialized successfully`);
        }
      } catch (error) {
        logger.error(`Failed to initialize service '${serviceName}'`, { error });
        throw error;
      }
    }
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{ healthy: boolean; services: Record<string, boolean> }> {
    const serviceNames = this.getRegisteredServices();
    const results: Record<string, boolean> = {};
    let allHealthy = true;

    for (const serviceName of serviceNames) {
      try {
        const service = this.resolve(serviceName);
        
        if (service && typeof service.isHealthy === 'function') {
          const isHealthy = await service.isHealthy();
          results[serviceName] = isHealthy;
          
          if (!isHealthy) {
            allHealthy = false;
          }
        } else {
          results[serviceName] = true; // Assume healthy if no health check method
        }
      } catch (error) {
        logger.error(`Health check failed for service '${serviceName}'`, { error });
        results[serviceName] = false;
        allHealthy = false;
      }
    }

    return {
      healthy: allHealthy,
      services: results
    };
  }
}

// Export singleton instance
export const container = DIContainer.getInstance();

/**
 * Service registration helper functions
 */
export function registerService<T>(
  name: string,
  factory: (container: DIContainer) => T,
  lifetime: ServiceLifetime = ServiceLifetime.Transient
): void {
  container.register(name, factory, lifetime);
}

export function registerSingleton<T>(
  name: string,
  factory: (container: DIContainer) => T
): void {
  container.registerSingleton(name, factory);
}

export function resolveService<T>(name: string): T {
  return container.resolve<T>(name);
}
