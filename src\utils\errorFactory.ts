/**
 * Error factory for creating standardized application errors
 * Provides consistent error creation with user-friendly messages
 */

import { 
  BaseError, 
  ValidationError, 
  ExternalServiceError, 
  NetworkError, 
  ConfigurationError,
  ErrorCategory, 
  ErrorSeverity, 
  FieldError,
  ErrorFactory as IErrorFactory
} from '../types';

/**
 * Implementation of the ErrorFactory interface
 */
export class ErrorFactory implements IErrorFactory {
  /**
   * Create a validation error with field-specific information
   */
  validation(message: string, fieldErrors: FieldError[], details?: Record<string, any>): ValidationError {
    return {
      code: 'E400_VALIDATION',
      message,
      userMessage: 'Please check your input and correct any errors before submitting.',
      category: ErrorCategory.Validation,
      severity: ErrorSeverity.Medium,
      statusCode: 400,
      fieldErrors,
      details,
      timestamp: new Date()
    };
  }

  /**
   * Create an authentication error
   */
  authentication(message: string, details?: Record<string, any>): BaseError {
    return {
      code: 'E401_AUTH',
      message,
      userMessage: 'You are not authorized to perform this action. Please check your credentials.',
      category: ErrorCategory.Authentication,
      severity: ErrorSeverity.Medium,
      statusCode: 401,
      details,
      timestamp: new Date()
    };
  }

  /**
   * Create an authorization error
   */
  authorization(message: string, details?: Record<string, any>): BaseError {
    return {
      code: 'E403_FORBIDDEN',
      message,
      userMessage: 'You do not have permission to access this resource.',
      category: ErrorCategory.Authorization,
      severity: ErrorSeverity.Medium,
      statusCode: 403,
      details,
      timestamp: new Date()
    };
  }

  /**
   * Create a not found error
   */
  notFound(resource: string, identifier?: string): BaseError {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    
    return {
      code: 'E404_NOT_FOUND',
      message,
      userMessage: `The requested ${resource.toLowerCase()} could not be found. It may have been removed or you may not have access to it.`,
      category: ErrorCategory.NotFound,
      severity: ErrorSeverity.Low,
      statusCode: 404,
      details: { resource, identifier },
      timestamp: new Date()
    };
  }

  /**
   * Create a conflict error
   */
  conflict(message: string, details?: Record<string, any>): BaseError {
    return {
      code: 'E409_CONFLICT',
      message,
      userMessage: 'This action conflicts with the current state. Please refresh and try again.',
      category: ErrorCategory.Conflict,
      severity: ErrorSeverity.Medium,
      statusCode: 409,
      details,
      timestamp: new Date()
    };
  }

  /**
   * Create a rate limit error
   */
  rateLimit(limit: number, window: string, retryAfter?: number): BaseError {
    return {
      code: 'E429_RATE_LIMIT',
      message: `Rate limit exceeded: ${limit} requests per ${window}`,
      userMessage: 'You are making requests too quickly. Please wait a moment before trying again.',
      category: ErrorCategory.RateLimit,
      severity: ErrorSeverity.Medium,
      statusCode: 429,
      details: { limit, window, retryAfter },
      timestamp: new Date()
    };
  }

  /**
   * Create an external service error
   */
  externalService(
    serviceName: string, 
    message: string, 
    options?: {
      endpoint?: string;
      externalCode?: string;
      externalMessage?: string;
      retryable?: boolean;
      retryAfter?: number;
    }
  ): ExternalServiceError {
    return {
      code: 'E502_EXTERNAL_SERVICE',
      message,
      userMessage: `Unable to connect to ${serviceName}. Please try again later.`,
      category: ErrorCategory.External,
      severity: ErrorSeverity.High,
      statusCode: 502,
      serviceName,
      endpoint: options?.endpoint,
      externalCode: options?.externalCode,
      externalMessage: options?.externalMessage,
      retryable: options?.retryable ?? true,
      retryAfter: options?.retryAfter,
      timestamp: new Date()
    };
  }

  /**
   * Create a network error
   */
  network(
    operation: 'connect' | 'read' | 'write' | 'timeout',
    message: string,
    options?: {
      host?: string;
      port?: number;
      retryable?: boolean;
    }
  ): NetworkError {
    const operationMessages = {
      connect: 'Unable to establish connection',
      read: 'Failed to read data',
      write: 'Failed to send data',
      timeout: 'Request timed out'
    };

    return {
      code: 'E503_NETWORK',
      message,
      userMessage: `${operationMessages[operation]}. Please check your internet connection and try again.`,
      category: ErrorCategory.Network,
      severity: ErrorSeverity.High,
      statusCode: 503,
      operation,
      host: options?.host,
      port: options?.port,
      retryable: options?.retryable ?? true,
      timestamp: new Date()
    };
  }

  /**
   * Create a configuration error
   */
  configuration(
    configKey: string,
    currentValue: any,
    expectedFormat: string,
    suggestion: string
  ): ConfigurationError {
    return {
      code: 'E500_CONFIG',
      message: `Invalid configuration for '${configKey}': expected ${expectedFormat}, got ${typeof currentValue}`,
      userMessage: 'There is a configuration issue. Please contact your administrator.',
      category: ErrorCategory.Configuration,
      severity: ErrorSeverity.Critical,
      statusCode: 500,
      configKey,
      currentValue,
      expectedFormat,
      suggestion,
      timestamp: new Date()
    };
  }

  /**
   * Create an internal server error
   */
  internal(message: string, details?: Record<string, any>): BaseError {
    return {
      code: 'E500_INTERNAL',
      message,
      userMessage: 'Something went wrong on our end. Please try again later or contact support if the problem persists.',
      category: ErrorCategory.Internal,
      severity: ErrorSeverity.High,
      statusCode: 500,
      details,
      timestamp: new Date()
    };
  }
}

// Export singleton instance
export const errorFactory = new ErrorFactory();

/**
 * Convenience functions for common error scenarios
 */
export const createDeviceNotFoundError = (deviceId: string) => 
  errorFactory.notFound('Device', deviceId);

export const createInvalidPhoneNumberError = (phoneNumber: string) => 
  errorFactory.validation('Invalid phone number format', [{
    field: 'phoneNumber',
    value: phoneNumber,
    message: 'Phone number must be in international format (e.g., +1234567890)',
    rule: 'international_format'
  }]);

export const createMessageTooLongError = (length: number, maxLength: number) => 
  errorFactory.validation('Message too long', [{
    field: 'message',
    value: length,
    message: `Message length (${length}) exceeds maximum allowed length (${maxLength})`,
    rule: 'max_length'
  }]);

export const createDeviceOfflineError = (deviceId: string) => 
  errorFactory.externalService('SMS Gateway', `Device ${deviceId} is offline`, {
    retryable: true,
    retryAfter: 60
  });

export const createRateLimitExceededError = (limit: number, windowMinutes: number) => 
  errorFactory.rateLimit(limit, `${windowMinutes} minutes`, windowMinutes * 60);

export const createNetworkTimeoutError = (host?: string) => 
  errorFactory.network('timeout', 'Request timed out', { host, retryable: true });

export const createConfigurationMissingError = (configKey: string) => 
  errorFactory.configuration(
    configKey, 
    undefined, 
    'non-empty string', 
    `Set the ${configKey} environment variable or configuration option`
  );
