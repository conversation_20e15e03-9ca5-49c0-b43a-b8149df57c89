/**
 * Base Controller class with standardized response handling
 * Provides common functionality for all API controllers
 */

import { Request, Response } from 'express';
import {
  PaginationOptions
} from '../types';
import { ErrorResponse } from '../types/errors';
import { 
  createSuccessResponse, 
  createPaginatedResponse, 
  createErrorResponse,
  validatePaginationOptions,
  ResponseTimer
} from '../utils/responseHelpers';
import { AppError } from '../middleware/errorHandler';
import logger from '../utils/logger';

/**
 * Base controller class with common response handling methods
 */
export abstract class BaseController {
  protected logger = logger;

  /**
   * Send a successful response
   * 
   * @param res - Express response object
   * @param data - Response data
   * @param message - Optional success message
   * @param statusCode - HTTP status code (default: 200)
   * @param metadata - Additional metadata
   */
  protected sendSuccess<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    metadata?: Record<string, any>
  ): void {
    const response = createSuccessResponse(data, message, metadata);
    res.status(statusCode).json(response);
  }

  /**
   * Send a paginated response
   * 
   * @param res - Express response object
   * @param data - Array of items
   * @param totalCount - Total number of items
   * @param pagination - Pagination options
   * @param filters - Applied filters
   * @param sort - Sort information
   * @param metadata - Additional metadata
   */
  protected sendPaginated<T>(
    res: Response,
    data: T[],
    totalCount: number,
    pagination: PaginationOptions,
    filters?: Record<string, any>,
    sort?: { field: string; order: 'asc' | 'desc' },
    metadata?: Record<string, any>
  ): void {
    const validatedPagination = validatePaginationOptions(pagination);
    const totalPages = Math.ceil(totalCount / validatedPagination.limit!);
    
    const paginationMeta = {
      page: validatedPagination.page!,
      limit: validatedPagination.limit!,
      total: totalCount,
      totalPages,
      hasNext: validatedPagination.page! < totalPages,
      hasPrev: validatedPagination.page! > 1,
      count: data.length,
      offset: (validatedPagination.page! - 1) * validatedPagination.limit!
    };

    const response = createPaginatedResponse(
      data,
      paginationMeta,
      filters,
      sort,
      metadata
    );
    
    res.status(200).json(response);
  }

  /**
   * Send an error response
   * 
   * @param res - Express response object
   * @param error - Error message or AppError instance
   * @param statusCode - HTTP status code
   * @param details - Additional error details
   */
  protected sendError(
    res: Response,
    error: string | AppError,
    statusCode: number = 500,
    details?: any
  ): void {
    if (error instanceof AppError) {
      const errorResponse: ErrorResponse = {
        success: false,
        error: error,
        timestamp: new Date().toISOString(),
        requestId: error.requestId || `req_${Date.now()}`
      };

      res.status(error.statusCode).json(errorResponse);
    } else {
      const response = createErrorResponse(error, statusCode, details);
      res.status(statusCode).json(response);
    }
  }

  /**
   * Send a created response (201)
   * 
   * @param res - Express response object
   * @param data - Created resource data
   * @param message - Success message
   * @param metadata - Additional metadata
   */
  protected sendCreated<T>(
    res: Response,
    data: T,
    message: string = 'Resource created successfully',
    metadata?: Record<string, any>
  ): void {
    this.sendSuccess(res, data, message, 201, metadata);
  }

  /**
   * Send a no content response (204)
   * 
   * @param res - Express response object
   */
  protected sendNoContent(res: Response): void {
    res.status(204).send();
  }

  /**
   * Extract pagination options from request query
   * 
   * @param req - Express request object
   * @returns Validated pagination options
   */
  protected getPaginationOptions(req: Request): PaginationOptions {
    const { page, limit, sortBy, sortOrder } = req.query;

    const options: PaginationOptions = {};

    if (page) options.page = parseInt(page as string, 10);
    if (limit) options.limit = parseInt(limit as string, 10);
    if (sortBy) options.sortBy = sortBy as string;
    if (sortOrder) options.sortOrder = sortOrder as 'asc' | 'desc';

    return validatePaginationOptions(options);
  }

  /**
   * Extract filters from request query
   * 
   * @param req - Express request object
   * @param allowedFilters - Array of allowed filter keys
   * @returns Extracted filters
   */
  protected getFilters(req: Request, allowedFilters: string[]): Record<string, any> {
    const filters: Record<string, any> = {};
    
    allowedFilters.forEach(key => {
      if (req.query[key] !== undefined) {
        filters[key] = req.query[key];
      }
    });
    
    return filters;
  }

  /**
   * Get request ID from headers or generate one
   * 
   * @param req - Express request object
   * @returns Request ID
   */
  protected getRequestId(req: Request): string {
    return req.headers['x-request-id'] as string || 
           `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Log request information
   * 
   * @param req - Express request object
   * @param action - Action being performed
   * @param metadata - Additional metadata
   */
  protected logRequest(req: Request, action: string, metadata?: Record<string, any>): void {
    this.logger.info(`Controller action: ${action}`, {
      method: req.method,
      url: req.url,
      requestId: this.getRequestId(req),
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      ...metadata
    });
  }

  /**
   * Validate required parameters
   * 
   * @param params - Parameters to validate
   * @param required - Array of required parameter names
   * @throws AppError if validation fails
   */
  protected validateRequiredParams(
    params: Record<string, any>,
    required: string[]
  ): void {
    const missing = required.filter(key => 
      params[key] === undefined || params[key] === null || params[key] === ''
    );
    
    if (missing.length > 0) {
      throw new AppError(
        `Missing required parameters: ${missing.join(', ')}`,
        400,
        true,
        {
          details: { missingParams: missing }
        }
      );
    }
  }

  /**
   * Create a response timer for performance monitoring
   * 
   * @returns ResponseTimer instance
   */
  protected createTimer(): ResponseTimer {
    return new ResponseTimer();
  }

  /**
   * Handle async controller methods with automatic error handling
   * 
   * @param fn - Async function to execute
   * @returns Express middleware function
   */
  protected asyncHandler(fn: (req: Request, res: Response) => Promise<void>) {
    return (req: Request, res: Response, next: (error?: any) => void) => {
      Promise.resolve(fn(req, res)).catch(next);
    };
  }

  /**
   * Validate and parse date parameters
   * 
   * @param dateString - Date string to parse
   * @param paramName - Parameter name for error messages
   * @returns Parsed Date object
   * @throws AppError if date is invalid
   */
  protected parseDate(dateString: string, paramName: string): Date {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      throw new AppError(
        `Invalid date format for parameter '${paramName}'`,
        400,
        true,
        {
          details: { 
            paramName, 
            value: dateString,
            expectedFormat: 'ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)'
          }
        }
      );
    }
    
    return date;
  }

  /**
   * Sanitize and validate string parameters
   * 
   * @param value - String value to sanitize
   * @param maxLength - Maximum allowed length
   * @param paramName - Parameter name for error messages
   * @returns Sanitized string
   * @throws AppError if validation fails
   */
  protected sanitizeString(value: string, maxLength: number, paramName: string): string {
    if (typeof value !== 'string') {
      throw new AppError(
        `Parameter '${paramName}' must be a string`,
        400,
        true,
        { details: { paramName, type: typeof value } }
      );
    }
    
    const sanitized = value.trim();
    
    if (sanitized.length > maxLength) {
      throw new AppError(
        `Parameter '${paramName}' exceeds maximum length of ${maxLength} characters`,
        400,
        true,
        { details: { paramName, length: sanitized.length, maxLength } }
      );
    }
    
    return sanitized;
  }
}
