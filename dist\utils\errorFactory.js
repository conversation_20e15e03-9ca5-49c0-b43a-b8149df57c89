"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createConfigurationMissingError = exports.createNetworkTimeoutError = exports.createRateLimitExceededError = exports.createDeviceOfflineError = exports.createMessageTooLongError = exports.createInvalidPhoneNumberError = exports.createDeviceNotFoundError = exports.errorFactory = exports.ErrorFactory = void 0;
const types_1 = require("../types");
class ErrorFactory {
    validation(message, fieldErrors, details) {
        return {
            code: 'E400_VALIDATION',
            message,
            userMessage: 'Please check your input and correct any errors before submitting.',
            category: types_1.ErrorCategory.Validation,
            severity: types_1.ErrorSeverity.Medium,
            statusCode: 400,
            fieldErrors,
            details,
            timestamp: new Date()
        };
    }
    authentication(message, details) {
        return {
            code: 'E401_AUTH',
            message,
            userMessage: 'You are not authorized to perform this action. Please check your credentials.',
            category: types_1.ErrorCategory.Authentication,
            severity: types_1.ErrorSeverity.Medium,
            statusCode: 401,
            details,
            timestamp: new Date()
        };
    }
    authorization(message, details) {
        return {
            code: 'E403_FORBIDDEN',
            message,
            userMessage: 'You do not have permission to access this resource.',
            category: types_1.ErrorCategory.Authorization,
            severity: types_1.ErrorSeverity.Medium,
            statusCode: 403,
            details,
            timestamp: new Date()
        };
    }
    notFound(resource, identifier) {
        const message = identifier
            ? `${resource} with identifier '${identifier}' not found`
            : `${resource} not found`;
        return {
            code: 'E404_NOT_FOUND',
            message,
            userMessage: `The requested ${resource.toLowerCase()} could not be found. It may have been removed or you may not have access to it.`,
            category: types_1.ErrorCategory.NotFound,
            severity: types_1.ErrorSeverity.Low,
            statusCode: 404,
            details: { resource, identifier },
            timestamp: new Date()
        };
    }
    conflict(message, details) {
        return {
            code: 'E409_CONFLICT',
            message,
            userMessage: 'This action conflicts with the current state. Please refresh and try again.',
            category: types_1.ErrorCategory.Conflict,
            severity: types_1.ErrorSeverity.Medium,
            statusCode: 409,
            details,
            timestamp: new Date()
        };
    }
    rateLimit(limit, window, retryAfter) {
        return {
            code: 'E429_RATE_LIMIT',
            message: `Rate limit exceeded: ${limit} requests per ${window}`,
            userMessage: 'You are making requests too quickly. Please wait a moment before trying again.',
            category: types_1.ErrorCategory.RateLimit,
            severity: types_1.ErrorSeverity.Medium,
            statusCode: 429,
            details: { limit, window, retryAfter },
            timestamp: new Date()
        };
    }
    externalService(serviceName, message, options) {
        return {
            code: 'E502_EXTERNAL_SERVICE',
            message,
            userMessage: `Unable to connect to ${serviceName}. Please try again later.`,
            category: types_1.ErrorCategory.External,
            severity: types_1.ErrorSeverity.High,
            statusCode: 502,
            serviceName,
            endpoint: options?.endpoint,
            externalCode: options?.externalCode,
            externalMessage: options?.externalMessage,
            retryable: options?.retryable ?? true,
            retryAfter: options?.retryAfter,
            timestamp: new Date()
        };
    }
    network(operation, message, options) {
        const operationMessages = {
            connect: 'Unable to establish connection',
            read: 'Failed to read data',
            write: 'Failed to send data',
            timeout: 'Request timed out'
        };
        return {
            code: 'E503_NETWORK',
            message,
            userMessage: `${operationMessages[operation]}. Please check your internet connection and try again.`,
            category: types_1.ErrorCategory.Network,
            severity: types_1.ErrorSeverity.High,
            statusCode: 503,
            operation,
            host: options?.host,
            port: options?.port,
            retryable: options?.retryable ?? true,
            timestamp: new Date()
        };
    }
    configuration(configKey, currentValue, expectedFormat, suggestion) {
        return {
            code: 'E500_CONFIG',
            message: `Invalid configuration for '${configKey}': expected ${expectedFormat}, got ${typeof currentValue}`,
            userMessage: 'There is a configuration issue. Please contact your administrator.',
            category: types_1.ErrorCategory.Configuration,
            severity: types_1.ErrorSeverity.Critical,
            statusCode: 500,
            configKey,
            currentValue,
            expectedFormat,
            suggestion,
            timestamp: new Date()
        };
    }
    internal(message, details) {
        return {
            code: 'E500_INTERNAL',
            message,
            userMessage: 'Something went wrong on our end. Please try again later or contact support if the problem persists.',
            category: types_1.ErrorCategory.Internal,
            severity: types_1.ErrorSeverity.High,
            statusCode: 500,
            details,
            timestamp: new Date()
        };
    }
}
exports.ErrorFactory = ErrorFactory;
exports.errorFactory = new ErrorFactory();
const createDeviceNotFoundError = (deviceId) => exports.errorFactory.notFound('Device', deviceId);
exports.createDeviceNotFoundError = createDeviceNotFoundError;
const createInvalidPhoneNumberError = (phoneNumber) => exports.errorFactory.validation('Invalid phone number format', [{
        field: 'phoneNumber',
        value: phoneNumber,
        message: 'Phone number must be in international format (e.g., +1234567890)',
        rule: 'international_format'
    }]);
exports.createInvalidPhoneNumberError = createInvalidPhoneNumberError;
const createMessageTooLongError = (length, maxLength) => exports.errorFactory.validation('Message too long', [{
        field: 'message',
        value: length,
        message: `Message length (${length}) exceeds maximum allowed length (${maxLength})`,
        rule: 'max_length'
    }]);
exports.createMessageTooLongError = createMessageTooLongError;
const createDeviceOfflineError = (deviceId) => exports.errorFactory.externalService('SMS Gateway', `Device ${deviceId} is offline`, {
    retryable: true,
    retryAfter: 60
});
exports.createDeviceOfflineError = createDeviceOfflineError;
const createRateLimitExceededError = (limit, windowMinutes) => exports.errorFactory.rateLimit(limit, `${windowMinutes} minutes`, windowMinutes * 60);
exports.createRateLimitExceededError = createRateLimitExceededError;
const createNetworkTimeoutError = (host) => exports.errorFactory.network('timeout', 'Request timed out', { host, retryable: true });
exports.createNetworkTimeoutError = createNetworkTimeoutError;
const createConfigurationMissingError = (configKey) => exports.errorFactory.configuration(configKey, undefined, 'non-empty string', `Set the ${configKey} environment variable or configuration option`);
exports.createConfigurationMissingError = createConfigurationMissingError;
//# sourceMappingURL=errorFactory.js.map