import { Request, Response, NextFunction } from 'express';
import { ApiResponse, ErrorResponse, BaseError, ErrorCategory, ErrorSeverity } from '../types';
import { createErrorResponse } from '../utils/responseHelpers';
import logger from '../utils/logger';

/**
 * Enhanced Application Error class with comprehensive error information
 */
export class AppError extends Error implements BaseError {
  public readonly code: string;
  public readonly userMessage: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: Record<string, any>;
  public readonly timestamp: Date;
  public readonly requestId?: string;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    options?: {
      code?: string;
      userMessage?: string;
      category?: ErrorCategory;
      severity?: ErrorSeverity;
      details?: Record<string, any>;
      requestId?: string;
    }
  ) {
    super(message);

    this.code = options?.code || `E${statusCode}`;
    this.message = message;
    this.userMessage = options?.userMessage || this.getUserFriendlyMessage(message, statusCode);
    this.category = options?.category || this.getErrorCategory(statusCode);
    this.severity = options?.severity || this.getErrorSeverity(statusCode);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = options?.details;
    this.timestamp = new Date();
    this.requestId = options?.requestId;

    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Get user-friendly error message
   * @private
   */
  private getUserFriendlyMessage(error: string, statusCode: number): string {
    const errorLower = error.toLowerCase();

    if (errorLower.includes('not found') || statusCode === 404) {
      return 'The requested resource could not be found.';
    }

    if (errorLower.includes('unauthorized') || statusCode === 401) {
      return 'You are not authorized to perform this action.';
    }

    if (errorLower.includes('forbidden') || statusCode === 403) {
      return 'You do not have permission to access this resource.';
    }

    if (errorLower.includes('validation') || statusCode === 400) {
      return 'Please check your input and correct any errors.';
    }

    if (statusCode >= 500) {
      return 'Something went wrong on our end. Please try again later.';
    }

    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Get error category based on status code
   * @private
   */
  private getErrorCategory(statusCode: number): ErrorCategory {
    if (statusCode === 400) return ErrorCategory.Validation;
    if (statusCode === 401) return ErrorCategory.Authentication;
    if (statusCode === 403) return ErrorCategory.Authorization;
    if (statusCode === 404) return ErrorCategory.NotFound;
    if (statusCode === 409) return ErrorCategory.Conflict;
    if (statusCode === 429) return ErrorCategory.RateLimit;
    if (statusCode >= 500) return ErrorCategory.Internal;
    return ErrorCategory.Internal;
  }

  /**
   * Get error severity based on status code
   * @private
   */
  private getErrorSeverity(statusCode: number): ErrorSeverity {
    if (statusCode >= 500) return ErrorSeverity.High;
    if (statusCode >= 400) return ErrorSeverity.Medium;
    return ErrorSeverity.Low;
  }
}

/**
 * Enhanced error handler with comprehensive error processing and logging
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let errorMessage = 'Internal Server Error';
  let userMessage = 'Something went wrong on our end. Please try again later.';
  let category = ErrorCategory.Internal;
  let severity = ErrorSeverity.High;
  let details: Record<string, any> | undefined;

  // Handle different error types
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    errorMessage = error.message;
    userMessage = error.userMessage;
    category = error.category;
    severity = error.severity;
    details = error.details;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    errorMessage = error.message;
    userMessage = 'Please check your input and correct any errors.';
    category = ErrorCategory.Validation;
    severity = ErrorSeverity.Medium;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
    errorMessage = 'Unauthorized';
    userMessage = 'You are not authorized to perform this action.';
    category = ErrorCategory.Authentication;
    severity = ErrorSeverity.Medium;
  } else if (error.name === 'CastError') {
    statusCode = 400;
    errorMessage = 'Invalid ID format';
    userMessage = 'The provided ID is not valid.';
    category = ErrorCategory.Validation;
    severity = ErrorSeverity.Low;
  } else if (error.name === 'MongoError' || error.name === 'MongooseError') {
    statusCode = 500;
    errorMessage = 'Database error';
    userMessage = 'A database error occurred. Please try again later.';
    category = ErrorCategory.Internal;
    severity = ErrorSeverity.High;
  }

  // Create request context for logging
  const requestContext = {
    requestId: req.headers['x-request-id'] as string,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.headers['x-user-id'] as string,
    timestamp: new Date().toISOString()
  };

  // Log error with appropriate level based on severity
  const logData = {
    error: errorMessage,
    statusCode,
    category,
    severity,
    details,
    stack: error.stack,
    request: requestContext
  };

  if (severity === ErrorSeverity.Critical || severity === ErrorSeverity.High) {
    logger.error('Application error occurred', logData);
  } else if (severity === ErrorSeverity.Medium) {
    logger.warn('Application warning', logData);
  } else {
    logger.info('Application info', logData);
  }

  // Create standardized error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      code: `E${statusCode}`,
      message: errorMessage,
      userMessage,
      category,
      severity,
      statusCode,
      details,
      timestamp: new Date(),
      requestId: requestContext.requestId,
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    },
    timestamp: new Date().toISOString(),
    requestId: requestContext.requestId
  };

  res.status(statusCode).json(errorResponse);
};

/**
 * Enhanced 404 handler with standardized response format
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const requestId = req.headers['x-request-id'] as string || `req_${Date.now()}`;

  logger.warn('Route not found', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId
  });

  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      code: 'E404',
      message: `Route ${req.originalUrl} not found`,
      userMessage: 'The requested page or resource could not be found.',
      category: ErrorCategory.NotFound,
      severity: ErrorSeverity.Low,
      statusCode: 404,
      timestamp: new Date(),
      requestId
    },
    timestamp: new Date().toISOString(),
    requestId
  };

  res.status(404).json(errorResponse);
};

/**
 * Enhanced async handler with better error context
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Add request ID if not present
    if (!req.headers['x-request-id']) {
      req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    Promise.resolve(fn(req, res, next)).catch((error: Error) => {
      // Add request context to error if it's an AppError
      if (error instanceof AppError && !error.requestId) {
        (error as any).requestId = req.headers['x-request-id'];
      }
      next(error);
    });
  };
};

/**
 * Validation error handler for request validation
 */
export const validationErrorHandler = (
  errors: Array<{ field: string; message: string; value?: any }>
): AppError => {
  const fieldErrors: Record<string, string[]> = {};

  errors.forEach(error => {
    if (!fieldErrors[error.field]) {
      fieldErrors[error.field] = [];
    }
    fieldErrors[error.field].push(error.message);
  });

  return new AppError(
    'Validation failed',
    400,
    true,
    {
      category: ErrorCategory.Validation,
      severity: ErrorSeverity.Medium,
      details: { fieldErrors }
    }
  );
};

/**
 * Rate limit error handler
 */
export const rateLimitErrorHandler = (
  limit: number,
  windowMs: number,
  retryAfter?: number
): AppError => {
  const windowMinutes = Math.ceil(windowMs / 60000);

  return new AppError(
    `Rate limit exceeded: ${limit} requests per ${windowMinutes} minutes`,
    429,
    true,
    {
      category: ErrorCategory.RateLimit,
      severity: ErrorSeverity.Medium,
      userMessage: 'You are making requests too quickly. Please wait a moment and try again.',
      details: {
        limit,
        windowMs,
        retryAfter: retryAfter || Math.ceil(windowMs / 1000)
      }
    }
  );
};
