import { Request, Response } from 'express';
import { BaseController } from './BaseController';
declare class DeviceController extends BaseController {
    getDevices: (req: Request, res: Response, next: (error?: any) => void) => void;
    getDevice: (req: Request, res: Response, next: (error?: any) => void) => void;
    deleteDevice: (req: Request, res: Response, next: (error?: any) => void) => void;
    getSettings: (req: Request, res: Response, next: (error?: any) => void) => void;
    updateSettings: (req: Request, res: Response, next: (error?: any) => void) => void;
    patchSettings: (req: Request, res: Response, next: (error?: any) => void) => void;
    getHealth: (req: Request, res: Response, next: (error?: any) => void) => void;
    getLogs: (req: Request, res: Response, next: (error?: any) => void) => void;
    exportInbox: (req: Request, res: Response, next: (error?: any) => void) => void;
    getDeviceStats: (req: Request, res: Response, next: (error?: any) => void) => void;
    testConnection: (req: Request, res: Response, next: (error?: any) => void) => void;
}
declare const _default: DeviceController;
export default _default;
//# sourceMappingURL=deviceController.d.ts.map