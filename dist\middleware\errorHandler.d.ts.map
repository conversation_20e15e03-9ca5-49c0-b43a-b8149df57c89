{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAC1D,OAAO,EAA8B,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAO/F,qBAAa,QAAS,SAAQ,KAAM,YAAW,SAAS;IACtD,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,WAAW,EAAE,MAAM,CAAC;IACpC,SAAgB,QAAQ,EAAE,aAAa,CAAC;IACxC,SAAgB,QAAQ,EAAE,aAAa,CAAC;IACxC,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,aAAa,EAAE,OAAO,CAAC;IACvC,SAAgB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9C,SAAgB,SAAS,EAAE,IAAI,CAAC;IAChC,SAAgB,SAAS,CAAC,EAAE,MAAM,CAAC;gBAGjC,OAAO,EAAE,MAAM,EACf,UAAU,GAAE,MAAY,EACxB,aAAa,GAAE,OAAc,EAC7B,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,QAAQ,CAAC,EAAE,aAAa,CAAC;QACzB,QAAQ,CAAC,EAAE,aAAa,CAAC;QACzB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB;IAsBH,OAAO,CAAC,sBAAsB;IA8B9B,OAAO,CAAC,gBAAgB;IAexB,OAAO,CAAC,gBAAgB;CAKzB;AAKD,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,EACZ,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,OAAO,YAAY,KAClB,IA4FF,CAAC;AAKF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,KAAG,IA4B7D,CAAC;AAKF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAcxD,CAAC;AAKF,eAAO,MAAM,sBAAsB,GACjC,QAAQ,KAAK,CAAC;IAAE,KAAK,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAC;IAAC,KAAK,CAAC,EAAE,GAAG,CAAA;CAAE,CAAC,KAC7D,QAoBF,CAAC;AAKF,eAAO,MAAM,qBAAqB,GAChC,OAAO,MAAM,EACb,UAAU,MAAM,EAChB,aAAa,MAAM,KAClB,QAkBF,CAAC"}