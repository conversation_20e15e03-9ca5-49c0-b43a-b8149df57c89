import fs from 'fs';
import path from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  meta?: any;
}

class Logger {
  private logLevel: LogLevel;
  private logFilePath?: string;

  constructor(level: string = 'info', filePath?: string | undefined) {
    this.logLevel = this.parseLogLevel(level);
    if (filePath !== undefined) {
      this.logFilePath = filePath;
    }

    if (this.logFilePath) {
      this.ensureLogDirectory();
    }
  }

  private parseLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'error':
        return LogLevel.ERROR;
      case 'warn':
        return LogLevel.WARN;
      case 'info':
        return LogLevel.INFO;
      case 'debug':
        return LogLevel.DEBUG;
      default:
        return LogLevel.INFO;
    }
  }

  private ensureLogDirectory(): void {
    if (!this.logFilePath) return;

    const logDir = path.dirname(this.logFilePath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...(meta && { meta }),
    };

    return JSON.stringify(logEntry);
  }

  private writeLog(level: LogLevel, levelName: string, message: string, meta?: any): void {
    if (level > this.logLevel) return;

    const formattedMessage = this.formatMessage(levelName, message, meta);

    // Console output
    console.log(formattedMessage);

    // File output
    if (this.logFilePath) {
      fs.appendFileSync(this.logFilePath, formattedMessage + '\n');
    }
  }

  public error(message: string, meta?: any): void {
    this.writeLog(LogLevel.ERROR, 'error', message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.writeLog(LogLevel.WARN, 'warn', message, meta);
  }

  public info(message: string, meta?: any): void {
    this.writeLog(LogLevel.INFO, 'info', message, meta);
  }

  public debug(message: string, meta?: any): void {
    this.writeLog(LogLevel.DEBUG, 'debug', message, meta);
  }
}

// Create default logger instance
const logger = new Logger(
  process.env['LOG_LEVEL'] || 'info',
  process.env['LOG_FILE_PATH']
);

export default logger;
