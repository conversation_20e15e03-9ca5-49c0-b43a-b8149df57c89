# SMS Gateway Backend API Documentation

This document provides comprehensive documentation for the SMS Gateway Backend API, including endpoints, request/response formats, and usage examples.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Response Format](#response-format)
4. [Error Handling](#error-handling)
5. [Device Management](#device-management)
6. [Settings Management](#settings-management)
7. [System Health](#system-health)
8. [Rate Limiting](#rate-limiting)

## Overview

The SMS Gateway Backend API provides a RESTful interface for managing SMS devices, configurations, and monitoring system health. All endpoints return JSON responses with consistent formatting.

**Base URL:** `http://localhost:3000/api`

**API Version:** `v1`

## Authentication

Currently, the API uses optional API key authentication. When enabled, include the API key in the request header:

```http
X-API-Key: your-api-key-here
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Optional success message",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_1705312200000_abc123",
  "metadata": {
    "processingTime": 150,
    "additionalInfo": "value"
  }
}
```

### Paginated Response
```json
{
  "success": true,
  "data": [ /* array of items */ ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false,
    "count": 20,
    "offset": 0
  },
  "filters": { /* applied filters */ },
  "sort": {
    "field": "name",
    "order": "asc"
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_1705312200000_abc123"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "E404",
    "message": "Device not found",
    "userMessage": "The requested device could not be found.",
    "category": "not_found",
    "severity": "low",
    "statusCode": 404,
    "timestamp": "2024-01-15T10:30:00.000Z",
    "requestId": "req_1705312200000_abc123"
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_1705312200000_abc123"
}
```

## Error Handling

The API uses standard HTTP status codes and provides detailed error information:

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 204 | No Content |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 429 | Too Many Requests |
| 500 | Internal Server Error |
| 502 | Bad Gateway |
| 503 | Service Unavailable |

## Device Management

### Get All Devices

Retrieve a list of all devices with optional filtering and pagination.

**Endpoint:** `GET /api/devices`

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20, max: 100)
- `name` (string, optional): Filter by device name (partial match)
- `status` (string, optional): Filter by status (`online`, `offline`, `recently_seen`, `deleted`)
- `active` (boolean, optional): Filter by active status
- `sortBy` (string, optional): Sort field (`name`, `lastSeen`, `createdAt`)
- `sortOrder` (string, optional): Sort order (`asc`, `desc`)

**Example Request:**
```http
GET /api/devices?page=1&limit=10&status=online&sortBy=name&sortOrder=asc
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "device-123",
      "name": "Primary SMS Device",
      "status": "online",
      "lastSeen": "2024-01-15T10:25:00.000Z",
      "isConnected": true,
      "messagesSentToday": 45,
      "successRate": 98.5
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false,
    "count": 1,
    "offset": 0
  }
}
```

### Get Device Details

Retrieve detailed information about a specific device.

**Endpoint:** `GET /api/devices/{deviceId}`

**Path Parameters:**
- `deviceId` (string, required): Device identifier

**Example Request:**
```http
GET /api/devices/device-123
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "device-123",
    "name": "Primary SMS Device",
    "status": "online",
    "lastSeen": "2024-01-15T10:25:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-15T10:25:00.000Z",
    "connection": {
      "isConnected": true,
      "lastSeen": "2024-01-15T10:25:00.000Z"
    },
    "capabilities": {
      "canSendSms": true,
      "canReceiveSms": true,
      "supportsDeliveryReports": true,
      "maxMessageLength": 160,
      "simSlots": [0, 1],
      "features": ["sms", "delivery_reports"]
    },
    "stats": {
      "messagesSent": 1250,
      "messagesReceived": 89,
      "messagesSentToday": 45,
      "successRate": 98.5,
      "avgResponseTime": 1200,
      "lastActivity": "2024-01-15T10:25:00.000Z"
    }
  }
}
```

### Delete Device

Soft delete a device (marks as deleted but preserves data).

**Endpoint:** `DELETE /api/devices/{deviceId}`

**Path Parameters:**
- `deviceId` (string, required): Device identifier

**Example Request:**
```http
DELETE /api/devices/device-123
```

**Example Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Device deleted successfully"
}
```

## Settings Management

### Get Device Settings

Retrieve current device settings and configuration.

**Endpoint:** `GET /api/devices/settings`

**Example Response:**
```json
{
  "success": true,
  "data": {
    "messages": {
      "limitPeriod": "PerHour",
      "limitValue": 100,
      "enableDeliveryReports": true,
      "maxRetryAttempts": 3
    },
    "webhooks": {
      "enabled": true,
      "url": "https://example.com/webhook",
      "retryCount": 3,
      "timeoutSeconds": 30
    },
    "gateway": {
      "name": "Primary Gateway",
      "autoReconnect": true,
      "heartbeatIntervalSeconds": 60
    }
  }
}
```

### Update Device Settings

Update device settings (full replacement).

**Endpoint:** `PUT /api/devices/settings`

**Request Body:**
```json
{
  "messages": {
    "limitPeriod": "PerHour",
    "limitValue": 150,
    "enableDeliveryReports": true
  },
  "webhooks": {
    "enabled": true,
    "url": "https://example.com/webhook"
  }
}
```

### Patch Device Settings

Partially update device settings.

**Endpoint:** `PATCH /api/devices/settings`

**Request Body:**
```json
{
  "messages": {
    "enableDeliveryReports": false
  }
}
```

## System Health

### Get System Health

Retrieve system health information and status of all components.

**Endpoint:** `GET /api/devices/health`

**Example Response:**
```json
{
  "success": true,
  "data": {
    "status": "pass",
    "version": "1.0.0",
    "releaseId": 1705312200000,
    "checks": {
      "database": {
        "status": "pass",
        "description": "Database connectivity",
        "observedValue": 15,
        "observedUnit": "ms",
        "lastChecked": "2024-01-15T10:30:00.000Z"
      },
      "smsGateway": {
        "status": "pass",
        "description": "SMS Gateway connectivity",
        "observedValue": 250,
        "observedUnit": "ms",
        "lastChecked": "2024-01-15T10:30:00.000Z"
      }
    },
    "uptime": 86400,
    "timestamp": "2024-01-15T10:30:00.000Z",
    "environment": "production"
  }
}
```

### Get System Logs

Retrieve system logs with optional date filtering.

**Endpoint:** `GET /api/devices/logs`

**Query Parameters:**
- `from` (string, optional): Start date (ISO 8601 format)
- `to` (string, optional): End date (ISO 8601 format)

**Example Request:**
```http
GET /api/devices/logs?from=2024-01-15T00:00:00.000Z&to=2024-01-15T23:59:59.999Z
```

### Test Connection

Test connectivity to the SMS Gateway.

**Endpoint:** `POST /api/devices/test-connection`

**Example Response:**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  "message": "Device connection successful"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default Limit:** 100 requests per 15 minutes per IP address
- **Headers:** Rate limit information is included in response headers:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time (Unix timestamp)

When rate limit is exceeded, the API returns a 429 status code:

```json
{
  "success": false,
  "error": {
    "code": "E429_RATE_LIMIT",
    "message": "Rate limit exceeded: 100 requests per 15 minutes",
    "userMessage": "You are making requests too quickly. Please wait a moment before trying again.",
    "category": "rate_limit",
    "severity": "medium",
    "statusCode": 429
  }
}
```

## SDK Examples

### JavaScript/TypeScript
```typescript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  }
});

// Get devices
const devices = await apiClient.get('/devices?status=online');

// Get device details
const device = await apiClient.get('/devices/device-123');

// Update settings
await apiClient.patch('/devices/settings', {
  messages: { enableDeliveryReports: true }
});
```

### cURL Examples
```bash
# Get devices
curl -X GET "http://localhost:3000/api/devices?status=online" \
  -H "X-API-Key: your-api-key"

# Get device details
curl -X GET "http://localhost:3000/api/devices/device-123" \
  -H "X-API-Key: your-api-key"

# Update settings
curl -X PATCH "http://localhost:3000/api/devices/settings" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"messages":{"enableDeliveryReports":true}}'
```
