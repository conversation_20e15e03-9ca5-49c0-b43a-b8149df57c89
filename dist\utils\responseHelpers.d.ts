import { ApiResponse, PaginatedResponse, ErrorResponse, PaginationMeta, PaginationOptions } from '../types';
export declare function generateRequestId(): string;
export declare function createSuccessResponse<T>(data: T, message?: string, metadata?: Record<string, any>): ApiResponse<T>;
export declare function createPaginatedResponse<T>(data: T[], pagination: PaginationMeta, filters?: Record<string, any>, sort?: {
    field: string;
    order: 'asc' | 'desc';
}, metadata?: Record<string, any>): PaginatedResponse<T>;
export declare function createErrorResponse(error: string, statusCode?: number, details?: any, fieldErrors?: Record<string, string[]>): ErrorResponse;
export declare function applyPagination<T>(items: T[], options?: PaginationOptions): {
    data: T[];
    pagination: PaginationMeta;
};
export declare function validatePaginationOptions(options?: PaginationOptions): PaginationOptions;
export declare function createOperationResult<T = any>(success: boolean, data?: T, error?: string, metadata?: Record<string, any>): {
    success: boolean;
    data?: T;
    error?: string;
    metadata?: Record<string, any>;
};
export declare class ResponseTimer {
    private startTime;
    constructor();
    getElapsed(): number;
    addTimingMetadata(metadata?: Record<string, any>): Record<string, any>;
}
export declare function createHealthResponse(status: 'pass' | 'warn' | 'fail', checks: Record<string, any>, version?: string): any;
//# sourceMappingURL=responseHelpers.d.ts.map