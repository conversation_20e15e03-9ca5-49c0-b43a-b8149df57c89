{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../src/types/errors/index.ts"], "names": [], "mappings": "AAQA,oBAAY,aAAa;IACvB,GAAG,QAAQ;IACX,MAAM,WAAW;IACjB,IAAI,SAAS;IACb,QAAQ,aAAa;CACtB;AAKD,oBAAY,aAAa;IACvB,UAAU,eAAe;IACzB,cAAc,mBAAmB;IACjC,aAAa,kBAAkB;IAC/B,QAAQ,cAAc;IACtB,QAAQ,aAAa;IACrB,SAAS,eAAe;IACxB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,aAAa,kBAAkB;CAChC;AAKD,MAAM,WAAW,SAAS;IAExB,IAAI,EAAE,MAAM,CAAC;IAEb,OAAO,EAAE,MAAM,CAAC;IAEhB,WAAW,EAAE,MAAM,CAAC;IAEpB,QAAQ,EAAE,aAAa,CAAC;IAExB,QAAQ,EAAE,aAAa,CAAC;IAExB,UAAU,EAAE,MAAM,CAAC;IAEnB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAE9B,SAAS,EAAE,IAAI,CAAC;IAEhB,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAKD,MAAM,WAAW,eAAgB,SAAQ,SAAS;IAChD,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC;IAEnC,WAAW,EAAE,UAAU,EAAE,CAAC;CAC3B;AAKD,MAAM,WAAW,UAAU;IAEzB,KAAK,EAAE,MAAM,CAAC;IAEd,KAAK,EAAE,GAAG,CAAC;IAEX,OAAO,EAAE,MAAM,CAAC;IAEhB,IAAI,EAAE,MAAM,CAAC;IAEb,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAKD,MAAM,WAAW,oBAAqB,SAAQ,SAAS;IACrD,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC;IAEjC,WAAW,EAAE,MAAM,CAAC;IAEpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB,SAAS,EAAE,OAAO,CAAC;IAEnB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAKD,MAAM,WAAW,YAAa,SAAQ,SAAS;IAC7C,QAAQ,EAAE,aAAa,CAAC,OAAO,CAAC;IAEhC,SAAS,EAAE,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;IAEpD,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,SAAS,EAAE,OAAO,CAAC;CACpB;AAKD,MAAM,WAAW,kBAAmB,SAAQ,SAAS;IACnD,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC;IAEtC,SAAS,EAAE,MAAM,CAAC;IAElB,YAAY,EAAE,GAAG,CAAC;IAElB,cAAc,EAAE,MAAM,CAAC;IAEvB,UAAU,EAAE,MAAM,CAAC;CACpB;AAKD,MAAM,WAAW,aAAa;IAE5B,OAAO,EAAE,KAAK,CAAC;IAEf,KAAK,EAAE,SAAS,CAAC;IAEjB,SAAS,EAAE,MAAM,CAAC;IAElB,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAKD,MAAM,WAAW,YAAY;IAI3B,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,eAAe,CAAC;IAKvG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;IAK1E,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;IAKzE,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAK3D,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;IAKpE,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAKzE,eAAe,CACb,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,SAAS,CAAC,EAAE,OAAO,CAAC;QACpB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,GACA,oBAAoB,CAAC;IAKxB,OAAO,CACL,SAAS,EAAE,SAAS,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,EACnD,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,SAAS,CAAC,EAAE,OAAO,CAAC;KACrB,GACA,YAAY,CAAC;IAKhB,aAAa,CACX,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,GAAG,EACjB,cAAc,EAAE,MAAM,EACtB,UAAU,EAAE,MAAM,GACjB,kBAAkB,CAAC;IAKtB,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;CACrE;AAKD,MAAM,WAAW,aAAa;IAI5B,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,SAAS,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,aAAa,CAAC;IAK/E,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;IAK3D,WAAW,CAAC,KAAK,EAAE,SAAS,GAAG,OAAO,CAAC;IAKvC,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC;CAC1D;AAKD,eAAO,MAAM,sBAAsB;;;;;;;;;;;CAWzB,CAAC"}