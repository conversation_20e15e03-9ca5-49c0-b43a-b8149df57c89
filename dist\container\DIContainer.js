"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.container = exports.DIContainer = exports.ServiceLifetime = void 0;
exports.registerService = registerService;
exports.registerSingleton = registerSingleton;
exports.resolveService = resolveService;
const serviceConfig_1 = require("../config/serviceConfig");
const logger_1 = __importDefault(require("../utils/logger"));
var ServiceLifetime;
(function (ServiceLifetime) {
    ServiceLifetime["Singleton"] = "singleton";
    ServiceLifetime["Transient"] = "transient";
    ServiceLifetime["Scoped"] = "scoped";
})(ServiceLifetime || (exports.ServiceLifetime = ServiceLifetime = {}));
class DIContainer {
    constructor() {
        this.services = new Map();
        this.singletons = new Map();
        this.scoped = new Map();
        this.registerCoreServices();
    }
    static getInstance() {
        if (!DIContainer.instance) {
            DIContainer.instance = new DIContainer();
        }
        return DIContainer.instance;
    }
    registerCoreServices() {
        this.register('logger', () => logger_1.default, ServiceLifetime.Singleton);
        this.register('configManager', () => serviceConfig_1.configManager, ServiceLifetime.Singleton);
        this.register('serviceContext', (container) => ({
            logger: container.resolve('logger'),
            config: serviceConfig_1.configManager.getServiceConfig('default')
        }), ServiceLifetime.Transient);
    }
    register(name, factory, lifetime = ServiceLifetime.Transient, dependencies = []) {
        this.services.set(name, {
            name,
            factory,
            lifetime,
            dependencies
        });
    }
    registerSingleton(name, factory, dependencies = []) {
        this.register(name, factory, ServiceLifetime.Singleton, dependencies);
    }
    registerTransient(name, factory, dependencies = []) {
        this.register(name, factory, ServiceLifetime.Transient, dependencies);
    }
    registerScoped(name, factory, dependencies = []) {
        this.register(name, factory, ServiceLifetime.Scoped, dependencies);
    }
    resolve(name) {
        const descriptor = this.services.get(name);
        if (!descriptor) {
            throw new Error(`Service '${name}' is not registered`);
        }
        this.checkCircularDependencies(name, new Set());
        switch (descriptor.lifetime) {
            case ServiceLifetime.Singleton:
                return this.resolveSingleton(descriptor);
            case ServiceLifetime.Scoped:
                return this.resolveScoped(descriptor);
            case ServiceLifetime.Transient:
            default:
                return this.resolveTransient(descriptor);
        }
    }
    resolveSingleton(descriptor) {
        if (this.singletons.has(descriptor.name)) {
            return this.singletons.get(descriptor.name);
        }
        const instance = descriptor.factory(this);
        this.singletons.set(descriptor.name, instance);
        return instance;
    }
    resolveScoped(descriptor) {
        if (this.scoped.has(descriptor.name)) {
            return this.scoped.get(descriptor.name);
        }
        const instance = descriptor.factory(this);
        this.scoped.set(descriptor.name, instance);
        return instance;
    }
    resolveTransient(descriptor) {
        return descriptor.factory(this);
    }
    checkCircularDependencies(serviceName, visited) {
        if (visited.has(serviceName)) {
            throw new Error(`Circular dependency detected: ${Array.from(visited).join(' -> ')} -> ${serviceName}`);
        }
        visited.add(serviceName);
        const descriptor = this.services.get(serviceName);
        if (descriptor?.dependencies) {
            for (const dependency of descriptor.dependencies) {
                this.checkCircularDependencies(dependency, new Set(visited));
            }
        }
    }
    isRegistered(name) {
        return this.services.has(name);
    }
    getRegisteredServices() {
        return Array.from(this.services.keys());
    }
    clearScoped() {
        this.scoped.clear();
    }
    async dispose() {
        for (const [name, instance] of this.singletons) {
            if (instance && typeof instance.dispose === 'function') {
                try {
                    await instance.dispose();
                }
                catch (error) {
                    logger_1.default.error(`Error disposing singleton service '${name}'`, { error });
                }
            }
        }
        for (const [name, instance] of this.scoped) {
            if (instance && typeof instance.dispose === 'function') {
                try {
                    await instance.dispose();
                }
                catch (error) {
                    logger_1.default.error(`Error disposing scoped service '${name}'`, { error });
                }
            }
        }
        this.singletons.clear();
        this.scoped.clear();
    }
    createScope() {
        const childContainer = Object.create(this);
        childContainer.scoped = new Map();
        return childContainer;
    }
    async initializeServices() {
        const serviceNames = this.getRegisteredServices();
        for (const serviceName of serviceNames) {
            try {
                const service = this.resolve(serviceName);
                if (service && typeof service.initialize === 'function') {
                    await service.initialize();
                    logger_1.default.info(`Service '${serviceName}' initialized successfully`);
                }
            }
            catch (error) {
                logger_1.default.error(`Failed to initialize service '${serviceName}'`, { error });
                throw error;
            }
        }
    }
    async healthCheck() {
        const serviceNames = this.getRegisteredServices();
        const results = {};
        let allHealthy = true;
        for (const serviceName of serviceNames) {
            try {
                const service = this.resolve(serviceName);
                if (service && typeof service.isHealthy === 'function') {
                    const isHealthy = await service.isHealthy();
                    results[serviceName] = isHealthy;
                    if (!isHealthy) {
                        allHealthy = false;
                    }
                }
                else {
                    results[serviceName] = true;
                }
            }
            catch (error) {
                logger_1.default.error(`Health check failed for service '${serviceName}'`, { error });
                results[serviceName] = false;
                allHealthy = false;
            }
        }
        return {
            healthy: allHealthy,
            services: results
        };
    }
}
exports.DIContainer = DIContainer;
exports.container = DIContainer.getInstance();
function registerService(name, factory, lifetime = ServiceLifetime.Transient) {
    exports.container.register(name, factory, lifetime);
}
function registerSingleton(name, factory) {
    exports.container.registerSingleton(name, factory);
}
function resolveService(name) {
    return exports.container.resolve(name);
}
//# sourceMappingURL=DIContainer.js.map