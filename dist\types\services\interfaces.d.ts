import { DeviceFilter, DeviceListItem, DeviceDetails, DeviceStats } from '../device/device';
import { DeviceSettings, UpdateSettingsRequest } from '../device/settings';
import { ApiResponse, PaginatedResponse, HealthResponse } from '../base/api';
import { PaginationOptions, OperationResult } from '../base/common';
export interface IDeviceService {
    getDevices(filter?: DeviceFilter, pagination?: PaginationOptions): Promise<PaginatedResponse<DeviceListItem>>;
    getDevice(deviceId: string): Promise<ApiResponse<DeviceDetails>>;
    deleteDevice(deviceId: string): Promise<OperationResult>;
    getSettings(deviceId?: string): Promise<ApiResponse<DeviceSettings>>;
    updateSettings(settings: DeviceSettings, deviceId?: string): Promise<OperationResult>;
    patchSettings(settings: UpdateSettingsRequest, deviceId?: string): Promise<OperationResult>;
    getHealth(): Promise<ApiResponse<HealthResponse>>;
    getLogs(from?: Date, to?: Date): Promise<ApiResponse<any[]>>;
    exportInbox(deviceId: string, since: Date, until: Date): Promise<OperationResult>;
    getDeviceStats(deviceId?: string): Promise<ApiResponse<DeviceStats>>;
    testConnection(): Promise<ApiResponse<{
        connected: boolean;
        timestamp: string;
    }>>;
}
export interface ServiceConfiguration {
    name: string;
    version: string;
    enabled: boolean;
    options: Record<string, any>;
    retry?: {
        attempts: number;
        delay: number;
        backoff: 'linear' | 'exponential';
    };
    timeout?: {
        connection: number;
        request: number;
    };
}
export interface ILogger {
    debug(message: string, meta?: Record<string, any>): void;
    info(message: string, meta?: Record<string, any>): void;
    warn(message: string, meta?: Record<string, any>): void;
    error(message: string, meta?: Record<string, any>): void;
}
export interface ICache {
    get<T>(key: string): Promise<T | null>;
    set<T>(key: string, value: T, ttl?: number): Promise<void>;
    delete(key: string): Promise<void>;
    clear(): Promise<void>;
    has(key: string): Promise<boolean>;
}
export interface IMetrics {
    increment(metric: string, tags?: Record<string, string>): void;
    decrement(metric: string, tags?: Record<string, string>): void;
    gauge(metric: string, value: number, tags?: Record<string, string>): void;
    timing(metric: string, duration: number, tags?: Record<string, string>): void;
    histogram(metric: string, value: number, tags?: Record<string, string>): void;
}
export interface ServiceContext {
    logger: ILogger;
    cache?: ICache;
    metrics?: IMetrics;
    config: ServiceConfiguration;
}
export interface IBaseService {
    readonly name: string;
    readonly version: string;
    readonly context: ServiceContext;
    initialize(): Promise<void>;
    cleanup(): Promise<void>;
    isHealthy(): Promise<boolean>;
}
//# sourceMappingURL=interfaces.d.ts.map