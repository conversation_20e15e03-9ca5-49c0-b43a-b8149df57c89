import { Request, Response } from 'express';
import deviceService from '../services/deviceService';
import { BaseController } from './BaseController';
import { DeviceFilter, DeviceSettings, UpdateSettingsRequest } from '../types';

/**
 * Device Controller with enhanced response handling and comprehensive device management
 *
 * Provides endpoints for:
 * - Device listing with filtering and pagination
 * - Device details and statistics
 * - Device configuration management
 * - Health monitoring and diagnostics
 * - Connection testing
 *
 * All responses are optimized for UI consumption with consistent formatting,
 * comprehensive error handling, and performance monitoring.
 */
class DeviceController extends BaseController {
  /**
   * Get all devices with filtering and pagination
   * GET /api/devices
   *
   * Query Parameters:
   * - page: Page number (default: 1)
   * - limit: Items per page (default: 20, max: 100)
   * - name: Filter by device name (partial match)
   * - status: Filter by device status (online, offline, recently_seen, deleted)
   * - active: Filter by active status (true/false)
   * - sortBy: Sort field (name, lastSeen, createdAt)
   * - sortOrder: Sort order (asc, desc)
   *
   * @example
   * GET /api/devices?page=1&limit=10&status=online&sortBy=name&sortOrder=asc
   */
  getDevices = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'getDevices');

    // Extract pagination and filters
    const pagination = this.getPaginationOptions(req);
    const filters = this.getFilters(req, [
      'name', 'status', 'active', 'lastSeenAfter', 'lastSeenBefore', 'isConnected'
    ]);

    // Convert query parameters to proper types
    const deviceFilter: DeviceFilter = {};

    if (filters['name']) deviceFilter.name = filters['name'] as string;
    if (filters['status']) deviceFilter.status = filters['status'] as any;
    if (filters['active']) deviceFilter.active = filters['active'] === 'true';
    if (filters['isConnected']) deviceFilter.isConnected = filters['isConnected'] === 'true';
    if (filters['lastSeenAfter']) deviceFilter.lastSeenAfter = this.parseDate(filters['lastSeenAfter'], 'lastSeenAfter');
    if (filters['lastSeenBefore']) deviceFilter.lastSeenBefore = this.parseDate(filters['lastSeenBefore'], 'lastSeenBefore');

    // Get devices from service
    const result = await deviceService.getDevices(deviceFilter, pagination);

    // Send paginated response
    this.sendPaginated(
      res,
      result.data || [],
      result.pagination.total,
      pagination,
      deviceFilter,
      pagination.sortBy ? { field: pagination.sortBy, order: pagination.sortOrder || 'desc' } : undefined,
      timer.addTimingMetadata({
        filtersApplied: Object.keys(deviceFilter).length,
        totalDevices: result.pagination.total
      })
    );
  });

  /**
   * Get device by ID with detailed information
   * GET /api/devices/:deviceId
   *
   * @param deviceId - Device identifier from URL parameters
   *
   * @example
   * GET /api/devices/device-123
   */
  getDevice = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const { deviceId } = req.params;

    this.logRequest(req, 'getDevice', { deviceId });

    // Validate required parameters
    this.validateRequiredParams({ deviceId }, ['deviceId']);

    // Get device details from service
    const result = await deviceService.getDevice(deviceId!);

    // Send success response
    this.sendSuccess(
      res,
      result.data,
      'Device details retrieved successfully',
      200,
      timer.addTimingMetadata({
        deviceId,
        hasConnectionInfo: !!result.data?.connection,
        hasStats: !!result.data?.stats
      })
    );
  });

  /**
   * Delete device (soft delete)
   * DELETE /api/devices/:deviceId
   *
   * @param deviceId - Device identifier from URL parameters
   *
   * @example
   * DELETE /api/devices/device-123
   */
  deleteDevice = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const { deviceId } = req.params;

    this.logRequest(req, 'deleteDevice', { deviceId });

    // Validate required parameters
    this.validateRequiredParams({ deviceId }, ['deviceId']);

    // Delete device through service
    await deviceService.deleteDevice(deviceId!);

    this.sendSuccess(res, null, 'Device deleted successfully', 200, timer.addTimingMetadata());
  });

  /**
   * Get device settings
   * GET /api/devices/settings
   *
   * @example
   * GET /api/devices/settings
   */
  getSettings = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'getSettings');

    const settings = await deviceService.getSettings();

    this.sendSuccess(
      res,
      settings,
      'Device settings retrieved successfully',
      200,
      timer.addTimingMetadata()
    );
  });

  /**
   * Update device settings (full update)
   * PUT /api/devices/settings
   *
   * @example
   * PUT /api/devices/settings
   * Content-Type: application/json
   *
   * {
   *   "messages": { "enableDeliveryReports": true },
   *   "webhooks": { "enabled": true, "url": "https://example.com/webhook" }
   * }
   */
  updateSettings = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const settings: DeviceSettings = req.body;

    this.logRequest(req, 'updateSettings', { settingsKeys: Object.keys(settings) });

    // Validate request body
    if (!settings || Object.keys(settings).length === 0) {
      this.sendError(res, 'Settings data is required', 400);
      return;
    }

    await deviceService.updateSettings(settings);

    this.sendSuccess(
      res,
      null,
      'Device settings updated successfully',
      200,
      timer.addTimingMetadata()
    );
  });

  /**
   * Partially update device settings
   * PATCH /api/devices/settings
   *
   * @example
   * PATCH /api/devices/settings
   * Content-Type: application/json
   *
   * {
   *   "messages": { "enableDeliveryReports": false }
   * }
   */
  patchSettings = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const settings: UpdateSettingsRequest = req.body;

    this.logRequest(req, 'patchSettings', { settingsKeys: Object.keys(settings) });

    // Validate request body
    if (!settings || Object.keys(settings).length === 0) {
      this.sendError(res, 'Settings data is required', 400);
      return;
    }

    await deviceService.patchSettings(settings);

    this.sendSuccess(
      res,
      null,
      'Device settings updated successfully',
      200,
      timer.addTimingMetadata()
    );
  });

  /**
   * Get system health information
   * GET /api/devices/health
   *
   * @example
   * GET /api/devices/health
   */
  getHealth = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'getHealth');

    const result = await deviceService.getHealth();

    this.sendSuccess(
      res,
      result.data,
      'System health retrieved successfully',
      200,
      timer.addTimingMetadata()
    );
  });

  /**
   * Get system logs
   * GET /api/devices/logs
   */
  getLogs = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const { from, to } = req.query;

    const fromDate = from && typeof from === 'string' ? this.parseDate(from, 'from') : undefined;
    const toDate = to && typeof to === 'string' ? this.parseDate(to, 'to') : undefined;

    this.logRequest(req, 'getLogs', { from: fromDate, to: toDate });

    const result = await deviceService.getLogs(fromDate, toDate);

    this.sendSuccess(
      res,
      result.data,
      'System logs retrieved successfully',
      200,
      timer.addTimingMetadata({
        dateRange: { from: fromDate, to: toDate },
        logCount: result.data?.length || 0
      })
    );
  });

  /**
   * Export inbox messages
   * POST /api/devices/:deviceId/export-inbox
   */
  exportInbox = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    const { deviceId } = req.params;
    const { since, until } = req.body;

    this.logRequest(req, 'exportInbox', { deviceId, since, until });

    // Validate required parameters
    this.validateRequiredParams({ deviceId, since, until }, ['deviceId', 'since', 'until']);

    const sinceDate = this.parseDate(since, 'since');
    const untilDate = this.parseDate(until, 'until');

    // Validate date range
    if (sinceDate >= untilDate) {
      this.sendError(res, 'Since date must be before until date', 400);
      return;
    }

    await deviceService.exportInbox(deviceId!, sinceDate, untilDate);

    this.sendSuccess(
      res,
      null,
      'Inbox export requested successfully',
      202, // Accepted - async operation
      timer.addTimingMetadata({
        deviceId,
        dateRange: { since: sinceDate, until: untilDate }
      })
    );
  });

  /**
   * Get device statistics and analytics
   * GET /api/devices/stats
   *
   * @example
   * GET /api/devices/stats
   */
  getDeviceStats = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'getDeviceStats');

    const stats = await deviceService.getDeviceStats();

    this.sendSuccess(
      res,
      stats,
      'Device statistics retrieved successfully',
      200,
      timer.addTimingMetadata()
    );
  });

  /**
   * Test device connection and connectivity
   * POST /api/devices/test-connection
   *
   * @example
   * POST /api/devices/test-connection
   */
  testConnection = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'testConnection');

    const isConnected = await deviceService.testConnection();

    this.sendSuccess(
      res,
      {
        connected: isConnected,
        timestamp: new Date().toISOString()
      },
      isConnected ? 'Device connection successful' : 'Device connection failed',
      200,
      timer.addTimingMetadata()
    );
  });
}

export default new DeviceController();
