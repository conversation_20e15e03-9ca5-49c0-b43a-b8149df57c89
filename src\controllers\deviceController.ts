import { Request, Response } from 'express';
import deviceService from '../services/deviceService';
import { BaseController } from './BaseController';
import { DeviceFilter, DeviceSettings, UpdateSettingsRequest } from '../types';

/**
 * Device Controller with enhanced response handling and comprehensive device management
 *
 * Provides endpoints for:
 * - Device listing with filtering and pagination
 * - Device details and statistics
 * - Device configuration management
 * - Health monitoring and diagnostics
 * - Connection testing
 *
 * All responses are optimized for UI consumption with consistent formatting,
 * comprehensive error handling, and performance monitoring.
 */
class DeviceController extends BaseController {
  /**
   * Get all devices with filtering and pagination
   * GET /api/devices
   *
   * Query Parameters:
   * - page: Page number (default: 1)
   * - limit: Items per page (default: 20, max: 100)
   * - name: Filter by device name (partial match)
   * - status: Filter by device status (online, offline, recently_seen, deleted)
   * - active: Filter by active status (true/false)
   * - sortBy: Sort field (name, lastSeen, createdAt)
   * - sortOrder: Sort order (asc, desc)
   *
   * @example
   * GET /api/devices?page=1&limit=10&status=online&sortBy=name&sortOrder=asc
   */
  getDevices = this.asyncHandler(async (req: Request, res: Response) => {
    const timer = this.createTimer();
    this.logRequest(req, 'getDevices');

    // Extract pagination and filters
    const pagination = this.getPaginationOptions(req);
    const filters = this.getFilters(req, [
      'name', 'status', 'active', 'lastSeenAfter', 'lastSeenBefore', 'isConnected'
    ]);

    // Convert query parameters to proper types
    const deviceFilter: DeviceFilter = {};

    if (filters['name']) deviceFilter.name = filters['name'] as string;
    if (filters['status']) deviceFilter.status = filters['status'] as any;
    if (filters['active']) deviceFilter.active = filters['active'] === 'true';
    if (filters['isConnected']) deviceFilter.isConnected = filters['isConnected'] === 'true';
    if (filters['lastSeenAfter']) deviceFilter.lastSeenAfter = this.parseDate(filters['lastSeenAfter'], 'lastSeenAfter');
    if (filters['lastSeenBefore']) deviceFilter.lastSeenBefore = this.parseDate(filters['lastSeenBefore'], 'lastSeenBefore');

    // Get devices from service
    const result = await deviceService.getDevices(deviceFilter, pagination);

    // Send paginated response
    this.sendPaginated(
      res,
      result.data || [],
      result.pagination.total,
      pagination,
      deviceFilter,
      pagination.sortBy ? { field: pagination.sortBy, order: pagination.sortOrder || 'desc' } : undefined,
      timer.addTimingMetadata({
        filtersApplied: Object.keys(deviceFilter).length,
        totalDevices: result.pagination.total
      })
    );
  });

  /**
   * Get device by ID
   * GET /api/devices/:deviceId
   */
  getDevice = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;

    logger.debug('Get device request received', { deviceId });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    const device = await deviceService.getDevice(deviceId);

    return res.json({
      success: true,
      data: device,
    });
  });

  /**
   * Delete device
   * DELETE /api/devices/:deviceId
   */
  deleteDevice = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;

    logger.info('Delete device request received', { deviceId });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    await deviceService.deleteDevice(deviceId);

    return res.json({
      success: true,
      message: 'Device deleted successfully',
    });
  });

  /**
   * Get device settings
   * GET /api/devices/settings
   */
  getSettings = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get device settings request received');

    const settings = await deviceService.getSettings();

    res.json({
      success: true,
      data: settings,
    });
  });

  /**
   * Update device settings (full update)
   * PUT /api/devices/settings
   */
  updateSettings = asyncHandler(async (req: Request, res: Response) => {
    const settings: DeviceSettings = req.body;

    logger.info('Update device settings request received', settings);

    await deviceService.updateSettings(settings);

    res.json({
      success: true,
      message: 'Device settings updated successfully',
    });
  });

  /**
   * Partially update device settings
   * PATCH /api/devices/settings
   */
  patchSettings = asyncHandler(async (req: Request, res: Response) => {
    const settings: Partial<DeviceSettings> = req.body;

    logger.info('Patch device settings request received', settings);

    await deviceService.patchSettings(settings);

    res.json({
      success: true,
      message: 'Device settings updated successfully',
    });
  });

  /**
   * Get system health
   * GET /api/devices/health
   */
  getHealth = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get system health request received');

    const health = await deviceService.getHealth();

    res.json({
      success: true,
      data: health,
    });
  });

  /**
   * Get system logs
   * GET /api/devices/logs
   */
  getLogs = asyncHandler(async (req: Request, res: Response) => {
    const { from, to } = req.query;

    const fromDate = from && typeof from === 'string' ? new Date(from) : undefined;
    const toDate = to && typeof to === 'string' ? new Date(to) : undefined;

    logger.debug('Get system logs request received', { from: fromDate, to: toDate });

    const logs = await deviceService.getLogs(fromDate, toDate);

    res.json({
      success: true,
      data: logs,
    });
  });

  /**
   * Export inbox messages
   * POST /api/devices/:deviceId/export-inbox
   */
  exportInbox = asyncHandler(async (req: Request, res: Response) => {
    const { deviceId } = req.params;
    const { since, until } = req.body;

    if (!since || !until) {
      return res.status(400).json({
        success: false,
        error: 'Both since and until dates are required',
      });
    }

    const sinceDate = new Date(since);
    const untilDate = new Date(until);

    logger.info('Export inbox request received', { deviceId, since: sinceDate, until: untilDate });

    if (!deviceId) {
      return res.status(400).json({ error: 'Device ID is required' });
    }
    await deviceService.exportInbox(deviceId, sinceDate, untilDate);

    return res.json({
      success: true,
      message: 'Inbox export requested successfully',
    });
  });

  /**
   * Get device statistics
   * GET /api/devices/stats
   */
  getDeviceStats = asyncHandler(async (_req: Request, res: Response) => {
    logger.debug('Get device stats request received');

    const stats = await deviceService.getDeviceStats();

    res.json({
      success: true,
      data: stats,
    });
  });

  /**
   * Test device connection
   * POST /api/devices/test-connection
   */
  testConnection = asyncHandler(async (_req: Request, res: Response) => {
    logger.info('Test device connection request received');

    const isConnected = await deviceService.testConnection();

    res.json({
      success: true,
      data: {
        connected: isConnected,
        timestamp: new Date().toISOString(),
      },
      message: isConnected ? 'Device connection successful' : 'Device connection failed',
    });
  });
}

export default new DeviceController();
