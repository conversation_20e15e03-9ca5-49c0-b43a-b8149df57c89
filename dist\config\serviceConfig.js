"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.configManager = exports.ConfigurationManager = void 0;
exports.validateConfiguration = validateConfiguration;
exports.getEnvironmentConfig = getEnvironmentConfig;
class ConfigurationManager {
    constructor() {
        this.config = new Map();
        this.loadConfiguration();
    }
    static getInstance() {
        if (!ConfigurationManager.instance) {
            ConfigurationManager.instance = new ConfigurationManager();
        }
        return ConfigurationManager.instance;
    }
    loadConfiguration() {
        this.config.set('server', {
            port: parseInt(process.env.PORT || '3000', 10),
            host: process.env.HOST || 'localhost',
            baseUrl: process.env.BASE_URL || 'http://localhost:3000',
            environment: process.env.NODE_ENV || 'development',
            cors: {
                origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
                credentials: process.env.CORS_CREDENTIALS === 'true'
            }
        });
        this.config.set('database', {
            path: process.env.DATABASE_PATH || './data/sms-gateway.db',
            maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10', 10),
            timeout: parseInt(process.env.DB_TIMEOUT || '30000', 10)
        });
        this.config.set('smsGateway', {
            baseUrl: process.env.SMS_GATEWAY_BASE_URL || 'http://localhost:8080',
            login: process.env.SMS_GATEWAY_LOGIN || '',
            password: process.env.SMS_GATEWAY_PASSWORD || '',
            timeout: parseInt(process.env.SMS_GATEWAY_TIMEOUT || '30000', 10),
            retryAttempts: parseInt(process.env.SMS_GATEWAY_RETRY_ATTEMPTS || '3', 10),
            retryDelay: parseInt(process.env.SMS_GATEWAY_RETRY_DELAY || '1000', 10)
        });
        this.config.set('lineBot', {
            channelSecret: process.env.LINE_CHANNEL_SECRET || '',
            channelAccessToken: process.env.LINE_CHANNEL_ACCESS_TOKEN || '',
            webhookPath: process.env.LINE_WEBHOOK_PATH || '/webhook/line',
            enabled: process.env.LINE_BOT_ENABLED === 'true'
        });
        this.config.set('logging', {
            level: process.env.LOG_LEVEL || 'info',
            file: {
                enabled: process.env.LOG_FILE_ENABLED === 'true',
                path: process.env.LOG_FILE_PATH || './logs/app.log',
                maxSize: process.env.LOG_FILE_MAX_SIZE || '10m',
                maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES || '5', 10)
            },
            console: {
                enabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
                colorize: process.env.LOG_CONSOLE_COLORIZE !== 'false'
            }
        });
        this.config.set('rateLimit', {
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
            max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
            message: process.env.RATE_LIMIT_MESSAGE || 'Too many requests from this IP',
            standardHeaders: process.env.RATE_LIMIT_STANDARD_HEADERS !== 'false',
            legacyHeaders: process.env.RATE_LIMIT_LEGACY_HEADERS === 'true'
        });
        this.config.set('webhooks', {
            smsPath: process.env.WEBHOOK_SMS_PATH || '/webhook/sms',
            timeout: parseInt(process.env.WEBHOOK_TIMEOUT || '10000', 10),
            retryAttempts: parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3', 10),
            retryDelay: parseInt(process.env.WEBHOOK_RETRY_DELAY || '1000', 10)
        });
        this.config.set('security', {
            helmet: {
                enabled: process.env.SECURITY_HELMET_ENABLED !== 'false',
                contentSecurityPolicy: process.env.SECURITY_CSP_ENABLED !== 'false'
            },
            apiKey: {
                enabled: process.env.API_KEY_ENABLED === 'true',
                header: process.env.API_KEY_HEADER || 'x-api-key',
                keys: process.env.API_KEYS?.split(',') || []
            }
        });
    }
    get(key) {
        return this.config.get(key);
    }
    set(key, value) {
        this.config.set(key, value);
    }
    has(key) {
        return this.config.has(key);
    }
    getAll() {
        const result = {};
        for (const [key, value] of this.config.entries()) {
            result[key] = value;
        }
        return result;
    }
    validate() {
        const errors = [];
        const smsGateway = this.get('smsGateway');
        if (!smsGateway.baseUrl) {
            errors.push('SMS_GATEWAY_BASE_URL is required');
        }
        if (!smsGateway.login) {
            errors.push('SMS_GATEWAY_LOGIN is required');
        }
        if (!smsGateway.password) {
            errors.push('SMS_GATEWAY_PASSWORD is required');
        }
        const lineBot = this.get('lineBot');
        if (lineBot.enabled) {
            if (!lineBot.channelSecret) {
                errors.push('LINE_CHANNEL_SECRET is required when LINE Bot is enabled');
            }
            if (!lineBot.channelAccessToken) {
                errors.push('LINE_CHANNEL_ACCESS_TOKEN is required when LINE Bot is enabled');
            }
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    getServiceConfig(serviceName) {
        const baseConfig = {
            name: serviceName,
            version: '1.0.0',
            enabled: true,
            options: {},
            retry: {
                attempts: 3,
                delay: 1000,
                backoff: 'exponential'
            },
            timeout: {
                connection: 30000,
                request: 30000
            }
        };
        switch (serviceName) {
            case 'deviceService':
                return {
                    ...baseConfig,
                    options: {
                        ...this.get('smsGateway'),
                        cacheEnabled: true,
                        cacheTtl: 300000
                    }
                };
            case 'smsService':
                return {
                    ...baseConfig,
                    options: {
                        maxMessageLength: 160,
                        defaultTtl: 3600000,
                        batchSize: 100
                    }
                };
            case 'lineService':
                return {
                    ...baseConfig,
                    enabled: this.get('lineBot').enabled,
                    options: this.get('lineBot')
                };
            case 'webhookService':
                return {
                    ...baseConfig,
                    options: this.get('webhooks')
                };
            default:
                return baseConfig;
        }
    }
}
exports.ConfigurationManager = ConfigurationManager;
exports.configManager = ConfigurationManager.getInstance();
function validateConfiguration() {
    const validation = exports.configManager.validate();
    if (!validation.valid) {
        console.error('Configuration validation failed:');
        validation.errors.forEach(error => console.error(`  - ${error}`));
        process.exit(1);
    }
    console.log('Configuration validation passed');
}
function getEnvironmentConfig() {
    const env = exports.configManager.get('server').environment;
    return {
        isDevelopment: env === 'development',
        isProduction: env === 'production',
        isTest: env === 'test',
        environment: env
    };
}
//# sourceMappingURL=serviceConfig.js.map