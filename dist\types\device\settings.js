"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogLevel = exports.SimSelectionMode = exports.LimitPeriod = void 0;
var LimitPeriod;
(function (LimitPeriod) {
    LimitPeriod["Disabled"] = "Disabled";
    LimitPeriod["PerMinute"] = "PerMinute";
    LimitPeriod["PerHour"] = "PerHour";
    LimitPeriod["PerDay"] = "PerDay";
})(LimitPeriod || (exports.LimitPeriod = LimitPeriod = {}));
var SimSelectionMode;
(function (SimSelectionMode) {
    SimSelectionMode["OSDefault"] = "OSDefault";
    SimSelectionMode["RoundRobin"] = "RoundRobin";
    SimSelectionMode["Random"] = "Random";
})(SimSelectionMode || (exports.SimSelectionMode = SimSelectionMode = {}));
var LogLevel;
(function (LogLevel) {
    LogLevel["Debug"] = "debug";
    LogLevel["Info"] = "info";
    LogLevel["Warn"] = "warn";
    LogLevel["Error"] = "error";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
//# sourceMappingURL=settings.js.map