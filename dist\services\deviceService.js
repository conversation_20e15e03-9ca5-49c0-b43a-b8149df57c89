"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const smsGatewayClient_1 = __importDefault(require("./smsGatewayClient"));
const types_1 = require("../types");
const logger_1 = __importDefault(require("../utils/logger"));
const errorHandler_1 = require("../middleware/errorHandler");
class DeviceService {
    constructor(context) {
        this.logger = context?.logger || logger_1.default;
        this.requestId = this.generateRequestId();
    }
    async getDevices(filter, pagination) {
        const startTime = Date.now();
        try {
            this.logger.debug('Getting devices with filters', {
                filter,
                pagination,
                requestId: this.requestId
            });
            const rawDevices = await smsGatewayClient_1.default.getDevices();
            const filteredDevices = this.applyDeviceFilters(rawDevices, filter);
            const deviceListItems = await this.convertToDeviceListItems(filteredDevices);
            const paginatedResult = this.applyPagination(deviceListItems, pagination);
            const duration = Date.now() - startTime;
            this.logger.debug('Devices retrieved successfully', {
                totalDevices: rawDevices.length,
                filteredCount: filteredDevices.length,
                returnedCount: paginatedResult.data.length,
                duration,
                requestId: this.requestId
            });
            return {
                success: true,
                data: paginatedResult.data,
                pagination: paginatedResult.pagination,
                timestamp: new Date().toISOString(),
                requestId: this.requestId,
                metadata: {
                    filters: filter,
                    processingTime: duration,
                    totalAvailable: rawDevices.length
                }
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Failed to get devices', {
                error: error.message,
                filter,
                pagination,
                duration,
                requestId: this.requestId,
                stack: error.stack
            });
            throw new errorHandler_1.AppError(`Failed to retrieve devices: ${error.message}`, 500, true);
        }
    }
    async getDevice(deviceId) {
        const startTime = Date.now();
        try {
            this.logger.debug('Getting device details', {
                deviceId,
                requestId: this.requestId
            });
            if (!deviceId?.trim()) {
                throw new errorHandler_1.AppError('Device ID is required', 400);
            }
            const devicesResponse = await this.getDevices();
            const devices = devicesResponse.data || [];
            const deviceListItem = devices.find(d => d.id === deviceId);
            if (!deviceListItem) {
                throw new errorHandler_1.AppError(`Device with ID '${deviceId}' not found`, 404, true);
            }
            const rawDevices = await smsGatewayClient_1.default.getDevices();
            const rawDevice = rawDevices.find(d => d.id === deviceId);
            if (!rawDevice) {
                throw new errorHandler_1.AppError(`Device data inconsistency for ID '${deviceId}'`, 500, true);
            }
            const deviceDetails = await this.convertToDeviceDetails(rawDevice);
            const duration = Date.now() - startTime;
            this.logger.debug('Device details retrieved successfully', {
                deviceId,
                deviceName: deviceDetails.name,
                status: deviceDetails.status,
                duration,
                requestId: this.requestId
            });
            return {
                success: true,
                data: deviceDetails,
                timestamp: new Date().toISOString(),
                requestId: this.requestId,
                metadata: {
                    processingTime: duration,
                    lastUpdated: deviceDetails.updatedAt
                }
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Failed to get device details', {
                error: error.message,
                deviceId,
                duration,
                requestId: this.requestId,
                stack: error.stack
            });
            if (error instanceof errorHandler_1.AppError) {
                throw error;
            }
            throw new errorHandler_1.AppError(`Failed to retrieve device details: ${error.message}`, 500, true);
        }
    }
    async deleteDevice(deviceId) {
        try {
            logger_1.default.info('Deleting device', { deviceId });
            await smsGatewayClient_1.default.deleteDevice(deviceId);
            logger_1.default.info('Device deleted successfully', { deviceId });
            return {
                success: true,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            logger_1.default.error('Failed to delete device', {
                error: error.message,
                deviceId,
            });
            return {
                success: false,
                error: `Failed to delete device: ${error.message}`,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    async getSettings(deviceId) {
        try {
            logger_1.default.debug('Getting device settings', { deviceId });
            const settings = await smsGatewayClient_1.default.getSettings();
            logger_1.default.debug('Device settings retrieved');
            return {
                success: true,
                data: settings,
                timestamp: new Date().toISOString(),
                requestId: this.generateRequestId()
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get device settings', {
                error: error.message,
                deviceId
            });
            throw new errorHandler_1.AppError(`Failed to get device settings: ${error.message}`, 500);
        }
    }
    async updateSettings(settings, deviceId) {
        try {
            logger_1.default.info('Updating device settings', { settings, deviceId });
            await smsGatewayClient_1.default.updateSettings(settings);
            logger_1.default.info('Device settings updated successfully');
            return {
                success: true,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            logger_1.default.error('Failed to update device settings', {
                error: error.message,
                settings,
                deviceId
            });
            return {
                success: false,
                error: `Failed to update device settings: ${error.message}`,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    async patchSettings(settings, deviceId) {
        try {
            logger_1.default.info('Partially updating device settings', { settings, deviceId });
            await smsGatewayClient_1.default.patchSettings(settings);
            logger_1.default.info('Device settings partially updated successfully');
            return {
                success: true,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            logger_1.default.error('Failed to partially update device settings', {
                error: error.message,
                settings,
                deviceId
            });
            return {
                success: false,
                error: `Failed to partially update device settings: ${error.message}`,
                metadata: {
                    deviceId,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    async getHealth() {
        try {
            logger_1.default.debug('Getting system health');
            const health = await smsGatewayClient_1.default.getHealth();
            logger_1.default.debug('System health retrieved', { status: health.status });
            return health;
        }
        catch (error) {
            logger_1.default.error('Failed to get system health', {
                error: error.message,
            });
            throw new errorHandler_1.AppError(`Failed to get system health: ${error.message}`, 500);
        }
    }
    async getLogs(from, to) {
        try {
            logger_1.default.debug('Getting system logs', { from, to });
            const logs = await smsGatewayClient_1.default.getLogs(from, to);
            logger_1.default.debug('System logs retrieved', { count: logs.length });
            return {
                success: true,
                data: logs,
                timestamp: new Date().toISOString(),
                requestId: this.generateRequestId()
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get system logs', {
                error: error.message,
                from,
                to,
            });
            throw new errorHandler_1.AppError(`Failed to get system logs: ${error.message}`, 500);
        }
    }
    async exportInbox(deviceId, since, until) {
        try {
            logger_1.default.info('Exporting inbox messages', { deviceId, since, until });
            await smsGatewayClient_1.default.exportInbox({
                deviceId,
                since,
                until,
            });
            logger_1.default.info('Inbox export requested successfully', { deviceId, since, until });
            return {
                success: true,
                metadata: {
                    deviceId,
                    since: since.toISOString(),
                    until: until.toISOString(),
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            logger_1.default.error('Failed to export inbox', {
                error: error.message,
                deviceId,
                since,
                until,
            });
            return {
                success: false,
                error: `Failed to export inbox: ${error.message}`,
                metadata: {
                    deviceId,
                    since: since.toISOString(),
                    until: until.toISOString(),
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    async getDeviceStats(deviceId) {
        try {
            logger_1.default.debug('Getting device statistics', { deviceId });
            const devicesResponse = await this.getDevices();
            const devices = devicesResponse.data || [];
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            const stats = {
                totalDevices: devices.length,
                activeDevices: devices.filter(d => d.status !== 'deleted').length,
                recentlySeenDevices: devices.filter(d => new Date(d.lastSeen) > oneDayAgo).length,
                devicesByStatus: [],
            };
            const statusGroups = devices.reduce((acc, device) => {
                const status = device.status;
                acc[status] = (acc[status] || 0) + 1;
                return acc;
            }, {});
            stats.devicesByStatus = Object.entries(statusGroups)
                .map(([status, count]) => ({ status, count: count }));
            logger_1.default.debug('Device statistics calculated', stats);
            return {
                success: true,
                data: stats,
                timestamp: new Date().toISOString(),
                requestId: this.generateRequestId()
            };
        }
        catch (error) {
            logger_1.default.error('Failed to get device statistics', {
                error: error.message,
            });
            throw new errorHandler_1.AppError(`Failed to get device statistics: ${error.message}`, 500);
        }
    }
    async testConnection() {
        try {
            logger_1.default.info('Testing device connection');
            const isConnected = await smsGatewayClient_1.default.testConnection();
            logger_1.default.info('Device connection test completed', { isConnected });
            return {
                success: true,
                data: {
                    connected: isConnected,
                    timestamp: new Date().toISOString()
                },
                timestamp: new Date().toISOString(),
                requestId: this.generateRequestId()
            };
        }
        catch (error) {
            logger_1.default.error('Device connection test failed', {
                error: error.message,
            });
            return {
                success: true,
                data: {
                    connected: false,
                    timestamp: new Date().toISOString()
                },
                timestamp: new Date().toISOString(),
                requestId: this.generateRequestId()
            };
        }
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    applyDeviceFilters(devices, filter) {
        if (!filter)
            return devices;
        return devices.filter(device => {
            if (filter.name && !device.name.toLowerCase().includes(filter.name.toLowerCase())) {
                return false;
            }
            if (filter.active !== undefined) {
                const isActive = !device.deletedAt;
                if (filter.active !== isActive)
                    return false;
            }
            if (filter.status) {
                const deviceStatus = this.determineDeviceStatus(device);
                const statusArray = Array.isArray(filter.status) ? filter.status : [filter.status];
                if (!statusArray.includes(deviceStatus))
                    return false;
            }
            if (filter.lastSeenAfter && new Date(device.lastSeen) < filter.lastSeenAfter) {
                return false;
            }
            if (filter.lastSeenBefore && new Date(device.lastSeen) > filter.lastSeenBefore) {
                return false;
            }
            if (filter.isConnected !== undefined) {
                const isConnected = this.isDeviceConnected(device);
                if (filter.isConnected !== isConnected)
                    return false;
            }
            return true;
        });
    }
    async convertToDeviceListItems(devices) {
        return devices.map(device => ({
            id: device.id,
            name: device.name,
            status: this.determineDeviceStatus(device),
            lastSeen: device.lastSeen,
            isConnected: this.isDeviceConnected(device),
            messagesSentToday: 0,
            successRate: 95
        }));
    }
    async convertToDeviceDetails(device) {
        return {
            ...device,
            status: this.determineDeviceStatus(device),
            connection: {
                isConnected: this.isDeviceConnected(device),
                lastSeen: new Date(device.lastSeen)
            },
            capabilities: {
                canSendSms: true,
                canReceiveSms: true,
                supportsDeliveryReports: true,
                maxMessageLength: 160,
                simSlots: [0, 1],
                features: ['sms', 'delivery_reports']
            },
            stats: {
                messagesSent: 0,
                messagesReceived: 0,
                messagesSentToday: 0,
                successRate: 95,
                avgResponseTime: 1500,
                lastActivity: new Date(device.lastSeen)
            },
            recentActivity: [],
            performance: {
                messagesPerHour: 0,
                avgDeliveryTime: 30,
                errorRate: 5,
                uptime: 98.5,
                trend: 'stable'
            }
        };
    }
    applyPagination(items, pagination) {
        const page = pagination?.page || 1;
        const limit = Math.min(pagination?.limit || 20, 100);
        const offset = (page - 1) * limit;
        const paginatedItems = items.slice(offset, offset + limit);
        const total = items.length;
        const totalPages = Math.ceil(total / limit);
        return {
            data: paginatedItems,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
                count: paginatedItems.length,
                offset
            }
        };
    }
    determineDeviceStatus(device) {
        if (device.deletedAt) {
            return types_1.DeviceStatus.Deleted;
        }
        const now = new Date();
        const lastSeen = new Date(device.lastSeen);
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        if (lastSeen > oneHourAgo) {
            return types_1.DeviceStatus.Online;
        }
        else if (lastSeen > oneDayAgo) {
            return types_1.DeviceStatus.RecentlySeen;
        }
        else {
            return types_1.DeviceStatus.Offline;
        }
    }
    isDeviceConnected(device) {
        const now = new Date();
        const lastSeen = new Date(device.lastSeen);
        const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
        return lastSeen > fiveMinutesAgo && !device.deletedAt;
    }
}
exports.default = new DeviceService();
//# sourceMappingURL=deviceService.js.map