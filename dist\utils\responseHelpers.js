"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseTimer = void 0;
exports.generateRequestId = generateRequestId;
exports.createSuccessResponse = createSuccessResponse;
exports.createPaginatedResponse = createPaginatedResponse;
exports.createErrorResponse = createErrorResponse;
exports.applyPagination = applyPagination;
exports.validatePaginationOptions = validatePaginationOptions;
exports.createOperationResult = createOperationResult;
exports.createHealthResponse = createHealthResponse;
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}
function createSuccessResponse(data, message, metadata) {
    return {
        success: true,
        data,
        message,
        timestamp: new Date().toISOString(),
        requestId: generateRequestId(),
        metadata
    };
}
function createPaginatedResponse(data, pagination, filters, sort, metadata) {
    return {
        success: true,
        data,
        pagination,
        filters,
        sort,
        timestamp: new Date().toISOString(),
        requestId: generateRequestId(),
        metadata
    };
}
function createErrorResponse(error, statusCode = 500, details, fieldErrors) {
    return {
        success: false,
        error: {
            code: `E${statusCode}`,
            message: error,
            userMessage: getUserFriendlyMessage(error, statusCode),
            category: getErrorCategory(statusCode),
            severity: getErrorSeverity(statusCode),
            statusCode,
            details,
            timestamp: new Date(),
            requestId: generateRequestId(),
            fieldErrors
        },
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
    };
}
function applyPagination(items, options) {
    const page = Math.max(options?.page || 1, 1);
    const limit = Math.min(Math.max(options?.limit || 20, 1), 100);
    const offset = (page - 1) * limit;
    const paginatedItems = items.slice(offset, offset + limit);
    const total = items.length;
    const totalPages = Math.ceil(total / limit);
    return {
        data: paginatedItems,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
            count: paginatedItems.length,
            offset
        }
    };
}
function getUserFriendlyMessage(error, statusCode) {
    const errorLower = error.toLowerCase();
    if (errorLower.includes('not found') || statusCode === 404) {
        return 'The requested resource could not be found.';
    }
    if (errorLower.includes('unauthorized') || statusCode === 401) {
        return 'You are not authorized to perform this action.';
    }
    if (errorLower.includes('forbidden') || statusCode === 403) {
        return 'You do not have permission to access this resource.';
    }
    if (errorLower.includes('validation') || statusCode === 400) {
        return 'Please check your input and correct any errors.';
    }
    if (errorLower.includes('rate limit') || statusCode === 429) {
        return 'You are making requests too quickly. Please wait a moment and try again.';
    }
    if (errorLower.includes('network') || errorLower.includes('connection')) {
        return 'Unable to connect to the service. Please check your connection and try again.';
    }
    if (statusCode >= 500) {
        return 'Something went wrong on our end. Please try again later.';
    }
    return 'An unexpected error occurred. Please try again.';
}
function getErrorCategory(statusCode) {
    if (statusCode === 400)
        return 'validation';
    if (statusCode === 401)
        return 'authentication';
    if (statusCode === 403)
        return 'authorization';
    if (statusCode === 404)
        return 'not_found';
    if (statusCode === 409)
        return 'conflict';
    if (statusCode === 429)
        return 'rate_limit';
    if (statusCode >= 500)
        return 'internal';
    return 'unknown';
}
function getErrorSeverity(statusCode) {
    if (statusCode >= 500)
        return 'high';
    if (statusCode >= 400)
        return 'medium';
    return 'low';
}
function validatePaginationOptions(options) {
    if (!options) {
        return { page: 1, limit: 20 };
    }
    return {
        page: Math.max(options.page || 1, 1),
        limit: Math.min(Math.max(options.limit || 20, 1), 100),
        sortBy: options.sortBy,
        sortOrder: options.sortOrder || 'desc'
    };
}
function createOperationResult(success, data, error, metadata) {
    return {
        success,
        data,
        error,
        metadata: {
            ...metadata,
            timestamp: new Date().toISOString(),
            requestId: generateRequestId()
        }
    };
}
class ResponseTimer {
    constructor() {
        this.startTime = Date.now();
    }
    getElapsed() {
        return Date.now() - this.startTime;
    }
    addTimingMetadata(metadata = {}) {
        return {
            ...metadata,
            processingTime: this.getElapsed(),
            timestamp: new Date().toISOString()
        };
    }
}
exports.ResponseTimer = ResponseTimer;
function createHealthResponse(status, checks, version = '1.0.0') {
    return {
        status,
        version,
        releaseId: Date.now(),
        checks,
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    };
}
//# sourceMappingURL=responseHelpers.js.map