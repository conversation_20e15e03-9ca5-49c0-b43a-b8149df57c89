/**
 * Comprehensive test examples for DeviceService
 * Demonstrates testing patterns and best practices for the refactored codebase
 */

import { DeviceService } from '../../src/services/deviceService';
import { DeviceStatus, DeviceFilter, PaginationOptions } from '../../src/types';
import { AppError } from '../../src/middleware/errorHandler';
import smsGatewayClient from '../../src/services/smsGatewayClient';

// Mock the SMS Gateway client
jest.mock('../../src/services/smsGatewayClient');
const mockSmsGatewayClient = smsGatewayClient as jest.Mocked<typeof smsGatewayClient>;

// Mock logger
jest.mock('../../src/utils/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('DeviceService', () => {
  let deviceService: DeviceService;

  beforeEach(() => {
    // Create a new instance for each test
    deviceService = new (DeviceService as any)();
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getDevices', () => {
    const mockDevices = [
      {
        id: 'device-1',
        name: 'Test Device 1',
        lastSeen: '2024-01-15T10:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:00:00.000Z',
        deletedAt: null
      },
      {
        id: 'device-2',
        name: 'Test Device 2',
        lastSeen: '2024-01-14T10:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-14T10:00:00.000Z',
        deletedAt: null
      }
    ];

    it('should return paginated device list successfully', async () => {
      // Arrange
      mockSmsGatewayClient.getDevices.mockResolvedValue(mockDevices);
      
      const filter: DeviceFilter = { status: DeviceStatus.Online };
      const pagination: PaginationOptions = { page: 1, limit: 10 };

      // Act
      const result = await deviceService.getDevices(filter, pagination);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
      expect(result.timestamp).toBeDefined();
      expect(result.requestId).toBeDefined();
      expect(mockSmsGatewayClient.getDevices).toHaveBeenCalledTimes(1);
    });

    it('should handle empty device list', async () => {
      // Arrange
      mockSmsGatewayClient.getDevices.mockResolvedValue([]);

      // Act
      const result = await deviceService.getDevices();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(result.pagination.total).toBe(0);
      expect(result.pagination.count).toBe(0);
    });

    it('should apply name filter correctly', async () => {
      // Arrange
      mockSmsGatewayClient.getDevices.mockResolvedValue(mockDevices);
      const filter: DeviceFilter = { name: 'Test Device 1' };

      // Act
      const result = await deviceService.getDevices(filter);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].name).toBe('Test Device 1');
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const manyDevices = Array.from({ length: 25 }, (_, i) => ({
        id: `device-${i + 1}`,
        name: `Test Device ${i + 1}`,
        lastSeen: '2024-01-15T10:00:00.000Z',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-15T10:00:00.000Z',
        deletedAt: null
      }));
      
      mockSmsGatewayClient.getDevices.mockResolvedValue(manyDevices);
      const pagination: PaginationOptions = { page: 2, limit: 10 };

      // Act
      const result = await deviceService.getDevices(undefined, pagination);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.length).toBe(10);
      expect(result.pagination.page).toBe(2);
      expect(result.pagination.total).toBe(25);
      expect(result.pagination.totalPages).toBe(3);
      expect(result.pagination.hasNext).toBe(true);
      expect(result.pagination.hasPrev).toBe(true);
    });

    it('should throw AppError when SMS Gateway fails', async () => {
      // Arrange
      const errorMessage = 'SMS Gateway connection failed';
      mockSmsGatewayClient.getDevices.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(deviceService.getDevices()).rejects.toThrow(AppError);
      await expect(deviceService.getDevices()).rejects.toThrow(`Failed to retrieve devices: ${errorMessage}`);
    });
  });

  describe('getDevice', () => {
    const mockDevice = {
      id: 'device-123',
      name: 'Test Device',
      lastSeen: '2024-01-15T10:00:00.000Z',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-15T10:00:00.000Z',
      deletedAt: null
    };

    it('should return device details successfully', async () => {
      // Arrange
      mockSmsGatewayClient.getDevices.mockResolvedValue([mockDevice]);

      // Act
      const result = await deviceService.getDevice('device-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.id).toBe('device-123');
      expect(result.data?.name).toBe('Test Device');
      expect(result.data?.connection).toBeDefined();
      expect(result.data?.capabilities).toBeDefined();
      expect(result.data?.stats).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.requestId).toBeDefined();
    });

    it('should throw AppError when device not found', async () => {
      // Arrange
      mockSmsGatewayClient.getDevices.mockResolvedValue([]);

      // Act & Assert
      await expect(deviceService.getDevice('non-existent')).rejects.toThrow(AppError);
      await expect(deviceService.getDevice('non-existent')).rejects.toThrow("Device with ID 'non-existent' not found");
    });

    it('should throw AppError when deviceId is empty', async () => {
      // Act & Assert
      await expect(deviceService.getDevice('')).rejects.toThrow(AppError);
      await expect(deviceService.getDevice('')).rejects.toThrow('Device ID is required');
    });
  });

  describe('deleteDevice', () => {
    it('should delete device successfully', async () => {
      // Arrange
      mockSmsGatewayClient.deleteDevice.mockResolvedValue(undefined);

      // Act
      const result = await deviceService.deleteDevice('device-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.metadata?.deviceId).toBe('device-123');
      expect(result.metadata?.timestamp).toBeDefined();
      expect(mockSmsGatewayClient.deleteDevice).toHaveBeenCalledWith('device-123');
    });

    it('should return error result when deletion fails', async () => {
      // Arrange
      const errorMessage = 'Device deletion failed';
      mockSmsGatewayClient.deleteDevice.mockRejectedValue(new Error(errorMessage));

      // Act
      const result = await deviceService.deleteDevice('device-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain(errorMessage);
      expect(result.metadata?.deviceId).toBe('device-123');
    });
  });

  describe('getSettings', () => {
    const mockSettings = {
      messages: {
        limitPeriod: 'PerHour',
        limitValue: 100,
        enableDeliveryReports: true
      },
      webhooks: {
        enabled: true,
        url: 'https://example.com/webhook'
      }
    };

    it('should return device settings successfully', async () => {
      // Arrange
      mockSmsGatewayClient.getSettings.mockResolvedValue(mockSettings);

      // Act
      const result = await deviceService.getSettings();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockSettings);
      expect(result.timestamp).toBeDefined();
      expect(result.requestId).toBeDefined();
    });

    it('should throw AppError when getting settings fails', async () => {
      // Arrange
      const errorMessage = 'Failed to get settings';
      mockSmsGatewayClient.getSettings.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(deviceService.getSettings()).rejects.toThrow(AppError);
      await expect(deviceService.getSettings()).rejects.toThrow(`Failed to get device settings: ${errorMessage}`);
    });
  });

  describe('updateSettings', () => {
    const mockSettings = {
      messages: {
        limitPeriod: 'PerHour' as const,
        limitValue: 150,
        enableDeliveryReports: false
      }
    };

    it('should update settings successfully', async () => {
      // Arrange
      mockSmsGatewayClient.updateSettings.mockResolvedValue(undefined);

      // Act
      const result = await deviceService.updateSettings(mockSettings);

      // Assert
      expect(result.success).toBe(true);
      expect(result.metadata?.timestamp).toBeDefined();
      expect(mockSmsGatewayClient.updateSettings).toHaveBeenCalledWith(mockSettings);
    });

    it('should return error result when update fails', async () => {
      // Arrange
      const errorMessage = 'Settings update failed';
      mockSmsGatewayClient.updateSettings.mockRejectedValue(new Error(errorMessage));

      // Act
      const result = await deviceService.updateSettings(mockSettings);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain(errorMessage);
    });
  });

  describe('testConnection', () => {
    it('should return connection success', async () => {
      // Arrange
      mockSmsGatewayClient.testConnection.mockResolvedValue(true);

      // Act
      const result = await deviceService.testConnection();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.connected).toBe(true);
      expect(result.data?.timestamp).toBeDefined();
      expect(result.timestamp).toBeDefined();
      expect(result.requestId).toBeDefined();
    });

    it('should return connection failure', async () => {
      // Arrange
      mockSmsGatewayClient.testConnection.mockResolvedValue(false);

      // Act
      const result = await deviceService.testConnection();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.connected).toBe(false);
    });

    it('should handle connection test error gracefully', async () => {
      // Arrange
      mockSmsGatewayClient.testConnection.mockRejectedValue(new Error('Connection test failed'));

      // Act
      const result = await deviceService.testConnection();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.connected).toBe(false);
    });
  });
});
