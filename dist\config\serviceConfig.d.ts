import { ServiceConfiguration } from '../types';
export declare class ConfigurationManager {
    private static instance;
    private config;
    private constructor();
    static getInstance(): ConfigurationManager;
    private loadConfiguration;
    get<T = any>(key: string): T;
    set(key: string, value: any): void;
    has(key: string): boolean;
    getAll(): Record<string, any>;
    validate(): {
        valid: boolean;
        errors: string[];
    };
    getServiceConfig(serviceName: string): ServiceConfiguration;
}
export declare const configManager: ConfigurationManager;
export declare function validateConfiguration(): void;
export declare function getEnvironmentConfig(): {
    isDevelopment: boolean;
    isProduction: boolean;
    isTest: boolean;
    environment: any;
};
//# sourceMappingURL=serviceConfig.d.ts.map