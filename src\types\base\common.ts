/**
 * Common base types and utility types used throughout the application
 * Provides foundational types for entities, pagination, filtering, and utilities
 */

// Base entity interface with comprehensive metadata
export interface BaseEntity {
  /** Unique identifier for the entity */
  id: string;
  /** Timestamp when the entity was created */
  createdAt: Date;
  /** Timestamp when the entity was last updated */
  updatedAt: Date;
}

// Soft delete support for entities
export interface SoftDeletableEntity extends BaseEntity {
  /** Timestamp when the entity was deleted, null if not deleted */
  deletedAt?: Date | null;
}

// Enhanced pagination types with comprehensive options
export interface PaginationOptions {
  /** Page number (1-based, default: 1) */
  page?: number;
  /** Number of items per page (1-100, default: 20) */
  limit?: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort order (default: 'desc') */
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationMeta {
  /** Current page number */
  page: number;
  /** Items per page */
  limit: number;
  /** Total number of items */
  total: number;
  /** Total number of pages */
  totalPages: number;
  /** Whether there is a next page */
  hasNext: boolean;
  /** Whether there is a previous page */
  hasPrev: boolean;
  /** Number of items on current page */
  count: number;
  /** Offset from the beginning */
  offset: number;
}

// Comprehensive process states for various operations
export enum ProcessState {
  Pending = 'pending',
  Processing = 'processing',
  Queued = 'queued',
  Sent = 'sent',
  Delivered = 'delivered',
  Failed = 'failed',
  Cancelled = 'cancelled',
  Expired = 'expired',
  Retrying = 'retrying',
}

// Enhanced filter interface with common filtering options
export interface BaseFilter {
  /** Filter by date range - start date */
  dateFrom?: Date;
  /** Filter by date range - end date */
  dateTo?: Date;
  /** Search query for text-based filtering */
  search?: string;
  /** Include soft-deleted items in results */
  includeDeleted?: boolean;
  /** Filter by status */
  status?: string | string[];
  /** Filter by creation date range */
  createdAfter?: Date;
  /** Filter by creation date range */
  createdBefore?: Date;
}

// Advanced utility types for better type safety and developer experience
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type OptionalNullable<T> = T | null | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// Date range utility for filtering
export interface DateRange {
  /** Start date (inclusive) */
  from: Date;
  /** End date (inclusive) */
  to: Date;
}

// Status tracking for entities
export interface StatusInfo {
  /** Current status code */
  status: string;
  /** Human-readable status message */
  message: string;
  /** Last status update timestamp */
  updatedAt: Date;
  /** Additional status metadata */
  metadata?: Record<string, any>;
}

// Database row mapping utility
export type DatabaseRow = Record<string, any>;

// Timestamp utilities for database operations
export interface TimestampFields {
  createdAt: Date;
  updatedAt: Date;
}

export interface OptionalTimestampFields {
  createdAt?: Date;
  updatedAt?: Date;
}

// Generic result type for operations that may succeed or fail
export interface OperationResult<T = any> {
  /** Whether the operation was successful */
  success: boolean;
  /** Result data if successful */
  data?: T;
  /** Error message if failed */
  error?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

// Configuration interface for services
export interface ServiceConfig {
  /** Service name */
  name: string;
  /** Service version */
  version: string;
  /** Whether the service is enabled */
  enabled: boolean;
  /** Service-specific configuration */
  options?: Record<string, any>;
}
