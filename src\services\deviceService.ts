import smsGatewayClient from './smsGatewayClient';
import {
  Device,
  DeviceSettings,
  DeviceFilter,
  DeviceListItem,
  DeviceDetails,
  DeviceStats,
  DeviceStatus,
  UpdateSettingsRequest,
  ApiResponse,
  PaginatedResponse,
  HealthResponse,
  PaginationOptions,
  PaginationMeta,
  OperationResult,
  IDeviceService,
  ServiceContext,
  ILogger
} from '../types';
import logger from '../utils/logger';
import { AppError } from '../middleware/errorHandler';

/**
 * Enhanced Device Service with comprehensive device management capabilities
 *
 * This service provides a complete interface for managing SMS Gateway devices,
 * including device discovery, configuration, health monitoring, and statistics.
 *
 * Features:
 * - Device listing with filtering and pagination
 * - Device configuration management
 * - Health monitoring and diagnostics
 * - Performance statistics and analytics
 * - UI-optimized response formats
 *
 * @example
 * ```typescript
 * // Get devices with filtering
 * const devices = await deviceService.getDevices(
 *   { status: DeviceStatus.Online },
 *   { page: 1, limit: 20 }
 * );
 *
 * // Update device settings
 * await deviceService.patchSettings({
 *   messages: { enableDeliveryReports: true }
 * });
 * ```
 */
class DeviceService implements IDeviceService {
  private readonly logger: ILogger;
  private readonly requestId: string;

  constructor(context?: Partial<ServiceContext>) {
    this.logger = context?.logger || logger;
    this.requestId = this.generateRequestId();
  }

  /**
   * Get all devices with optional filtering and pagination
   *
   * Retrieves a list of devices from the SMS Gateway with support for:
   * - Status filtering (online, offline, recently seen, deleted)
   * - Name-based searching
   * - Date range filtering
   * - Pagination with metadata
   *
   * @param filter - Optional filter criteria for device selection
   * @param pagination - Optional pagination parameters
   * @returns Promise resolving to paginated device list optimized for UI display
   *
   * @example
   * ```typescript
   * // Get online devices only
   * const result = await deviceService.getDevices(
   *   { status: DeviceStatus.Online },
   *   { page: 1, limit: 10 }
   * );
   *
   * console.log(`Found ${result.data?.length} devices`);
   * console.log(`Total: ${result.pagination.total}`);
   * ```
   */
  async getDevices(
    filter?: DeviceFilter,
    pagination?: PaginationOptions
  ): Promise<PaginatedResponse<DeviceListItem>> {
    const startTime = Date.now();

    try {
      this.logger.debug('Getting devices with filters', {
        filter,
        pagination,
        requestId: this.requestId
      });

      // Get raw devices from SMS Gateway
      const rawDevices = await smsGatewayClient.getDevices();

      // Apply filtering
      const filteredDevices = this.applyDeviceFilters(rawDevices, filter);

      // Convert to UI-optimized format
      const deviceListItems = await this.convertToDeviceListItems(filteredDevices);

      // Apply pagination
      const paginatedResult = this.applyPagination(deviceListItems, pagination);

      const duration = Date.now() - startTime;

      this.logger.debug('Devices retrieved successfully', {
        totalDevices: rawDevices.length,
        filteredCount: filteredDevices.length,
        returnedCount: paginatedResult.data.length,
        duration,
        requestId: this.requestId
      });

      return {
        success: true,
        data: paginatedResult.data,
        pagination: paginatedResult.pagination,
        timestamp: new Date().toISOString(),
        requestId: this.requestId,
        metadata: {
          filters: filter,
          processingTime: duration,
          totalAvailable: rawDevices.length
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error('Failed to get devices', {
        error: (error as Error).message,
        filter,
        pagination,
        duration,
        requestId: this.requestId,
        stack: (error as Error).stack
      });

      throw new AppError(
        `Failed to retrieve devices: ${(error as Error).message}`,
        500,
        true
      );
    }
  }

  /**
   * Get a specific device by ID with detailed information
   *
   * Retrieves comprehensive device information including:
   * - Basic device properties
   * - Connection status and health
   * - Recent activity and performance metrics
   * - Configuration status
   *
   * @param deviceId - Unique device identifier
   * @returns Promise resolving to detailed device information
   *
   * @throws {AppError} When device is not found (404) or retrieval fails (500)
   *
   * @example
   * ```typescript
   * try {
   *   const deviceDetails = await deviceService.getDevice('device-123');
   *   console.log(`Device: ${deviceDetails.data.name}`);
   *   console.log(`Status: ${deviceDetails.data.status}`);
   * } catch (error) {
   *   if (error.statusCode === 404) {
   *     console.log('Device not found');
   *   }
   * }
   * ```
   */
  async getDevice(deviceId: string): Promise<ApiResponse<DeviceDetails>> {
    const startTime = Date.now();

    try {
      this.logger.debug('Getting device details', {
        deviceId,
        requestId: this.requestId
      });

      if (!deviceId?.trim()) {
        throw new AppError('Device ID is required', 400);
      }

      // Get all devices and find the specific one
      const devicesResponse = await this.getDevices();
      const devices = devicesResponse.data || [];

      const deviceListItem = devices.find(d => d.id === deviceId);

      if (!deviceListItem) {
        throw new AppError(
          `Device with ID '${deviceId}' not found`,
          404,
          true
        );
      }

      // Get raw device data for detailed information
      const rawDevices = await smsGatewayClient.getDevices();
      const rawDevice = rawDevices.find(d => d.id === deviceId);

      if (!rawDevice) {
        throw new AppError(
          `Device data inconsistency for ID '${deviceId}'`,
          500,
          true
        );
      }

      // Convert to detailed device information
      const deviceDetails = await this.convertToDeviceDetails(rawDevice);

      const duration = Date.now() - startTime;

      this.logger.debug('Device details retrieved successfully', {
        deviceId,
        deviceName: deviceDetails.name,
        status: deviceDetails.status,
        duration,
        requestId: this.requestId
      });

      return {
        success: true,
        data: deviceDetails,
        timestamp: new Date().toISOString(),
        requestId: this.requestId,
        metadata: {
          processingTime: duration,
          lastUpdated: deviceDetails.updatedAt
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error('Failed to get device details', {
        error: (error as Error).message,
        deviceId,
        duration,
        requestId: this.requestId,
        stack: (error as Error).stack
      });

      if (error instanceof AppError) {
        throw error;
      }

      throw new AppError(
        `Failed to retrieve device details: ${(error as Error).message}`,
        500,
        true
      );
    }
  }

  /**
   * Delete device
   */
  async deleteDevice(deviceId: string): Promise<void> {
    try {
      logger.info('Deleting device', { deviceId });

      await smsGatewayClient.deleteDevice(deviceId);

      logger.info('Device deleted successfully', { deviceId });
    } catch (error) {
      logger.error('Failed to delete device', {
        error: (error as Error).message,
        deviceId,
      });

      throw new AppError(
        `Failed to delete device: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get device settings
   */
  async getSettings(): Promise<DeviceSettings> {
    try {
      logger.debug('Getting device settings');

      const settings = await smsGatewayClient.getSettings();

      logger.debug('Device settings retrieved');

      return settings;
    } catch (error) {
      logger.error('Failed to get device settings', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Update device settings (full update)
   */
  async updateSettings(settings: DeviceSettings): Promise<void> {
    try {
      logger.info('Updating device settings', settings);

      await smsGatewayClient.updateSettings(settings);

      logger.info('Device settings updated successfully');
    } catch (error) {
      logger.error('Failed to update device settings', {
        error: (error as Error).message,
        settings,
      });

      throw new AppError(
        `Failed to update device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Partially update device settings
   */
  async patchSettings(settings: Partial<DeviceSettings>): Promise<void> {
    try {
      logger.info('Partially updating device settings', settings);

      await smsGatewayClient.patchSettings(settings);

      logger.info('Device settings partially updated successfully');
    } catch (error) {
      logger.error('Failed to partially update device settings', {
        error: (error as Error).message,
        settings,
      });

      throw new AppError(
        `Failed to partially update device settings: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get system health
   */
  async getHealth(): Promise<any> {
    try {
      logger.debug('Getting system health');

      const health = await smsGatewayClient.getHealth();

      logger.debug('System health retrieved', { status: health.status });

      return health;
    } catch (error) {
      logger.error('Failed to get system health', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get system health: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get system logs
   */
  async getLogs(from?: Date, to?: Date): Promise<any[]> {
    try {
      logger.debug('Getting system logs', { from, to });

      const logs = await smsGatewayClient.getLogs(from, to);

      logger.debug('System logs retrieved', { count: logs.length });

      return logs;
    } catch (error) {
      logger.error('Failed to get system logs', {
        error: (error as Error).message,
        from,
        to,
      });

      throw new AppError(
        `Failed to get system logs: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Export inbox messages
   */
  async exportInbox(deviceId: string, since: Date, until: Date): Promise<void> {
    try {
      logger.info('Exporting inbox messages', { deviceId, since, until });

      await smsGatewayClient.exportInbox({
        deviceId,
        since,
        until,
      });

      logger.info('Inbox export requested successfully', { deviceId, since, until });
    } catch (error) {
      logger.error('Failed to export inbox', {
        error: (error as Error).message,
        deviceId,
        since,
        until,
      });

      throw new AppError(
        `Failed to export inbox: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Get device statistics
   */
  async getDeviceStats(): Promise<{
    totalDevices: number;
    activeDevices: number;
    recentlySeenDevices: number;
    devicesByStatus: Array<{ status: string; count: number }>;
  }> {
    try {
      logger.debug('Getting device statistics');

      const devices = await this.getDevices();
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const stats = {
        totalDevices: devices.length,
        activeDevices: devices.filter(d => !d.deletedAt).length,
        recentlySeenDevices: devices.filter(d => 
          new Date(d.lastSeen) > oneDayAgo
        ).length,
        devicesByStatus: [] as Array<{ status: string; count: number }>,
      };

      // Group devices by status
      const statusGroups = devices.reduce((acc, device) => {
        let status = 'offline';
        
        if (device.deletedAt) {
          status = 'deleted';
        } else if (new Date(device.lastSeen) > oneHourAgo) {
          status = 'online';
        } else if (new Date(device.lastSeen) > oneDayAgo) {
          status = 'recently_seen';
        }

        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      stats.devicesByStatus = Object.entries(statusGroups)
        .map(([status, count]) => ({ status, count }));

      logger.debug('Device statistics calculated', stats);

      return stats;
    } catch (error) {
      logger.error('Failed to get device statistics', {
        error: (error as Error).message,
      });

      throw new AppError(
        `Failed to get device statistics: ${(error as Error).message}`,
        500
      );
    }
  }

  /**
   * Test device connection
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('Testing device connection');

      const isConnected = await smsGatewayClient.testConnection();

      logger.info('Device connection test completed', { isConnected });

      return isConnected;
    } catch (error) {
      logger.error('Device connection test failed', {
        error: (error as Error).message,
      });

      return false;
    }
  }
  /**
   * Generate a unique request ID for tracking
   * @private
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Apply filters to device list
   * @private
   */
  private applyDeviceFilters(devices: Device[], filter?: DeviceFilter): Device[] {
    if (!filter) return devices;

    return devices.filter(device => {
      // Name filter (case-insensitive partial match)
      if (filter.name && !device.name.toLowerCase().includes(filter.name.toLowerCase())) {
        return false;
      }

      // Active filter (non-deleted devices)
      if (filter.active !== undefined) {
        const isActive = !device.deletedAt;
        if (filter.active !== isActive) return false;
      }

      // Status filter
      if (filter.status) {
        const deviceStatus = this.determineDeviceStatus(device);
        const statusArray = Array.isArray(filter.status) ? filter.status : [filter.status];
        if (!statusArray.includes(deviceStatus)) return false;
      }

      // Date range filters
      if (filter.lastSeenAfter && new Date(device.lastSeen) < filter.lastSeenAfter) {
        return false;
      }

      if (filter.lastSeenBefore && new Date(device.lastSeen) > filter.lastSeenBefore) {
        return false;
      }

      // Connection status filter
      if (filter.isConnected !== undefined) {
        const isConnected = this.isDeviceConnected(device);
        if (filter.isConnected !== isConnected) return false;
      }

      return true;
    });
  }

  /**
   * Convert raw devices to UI-optimized list items
   * @private
   */
  private async convertToDeviceListItems(devices: Device[]): Promise<DeviceListItem[]> {
    return devices.map(device => ({
      id: device.id,
      name: device.name,
      status: this.determineDeviceStatus(device),
      lastSeen: device.lastSeen,
      isConnected: this.isDeviceConnected(device),
      messagesSentToday: 0, // Would be calculated from message statistics
      successRate: 95 // Would be calculated from delivery statistics
    }));
  }

  /**
   * Convert raw device to detailed device information
   * @private
   */
  private async convertToDeviceDetails(device: Device): Promise<DeviceDetails> {
    return {
      ...device,
      status: this.determineDeviceStatus(device),
      connection: {
        isConnected: this.isDeviceConnected(device),
        lastSeen: new Date(device.lastSeen)
      },
      capabilities: {
        canSendSms: true,
        canReceiveSms: true,
        supportsDeliveryReports: true,
        maxMessageLength: 160,
        simSlots: [0, 1],
        features: ['sms', 'delivery_reports']
      },
      stats: {
        messagesSent: 0,
        messagesReceived: 0,
        messagesSentToday: 0,
        successRate: 95,
        avgResponseTime: 1500,
        lastActivity: new Date(device.lastSeen)
      },
      recentActivity: [],
      performance: {
        messagesPerHour: 0,
        avgDeliveryTime: 30,
        errorRate: 5,
        uptime: 98.5,
        trend: 'stable'
      }
    };
  }

  /**
   * Apply pagination to device list
   * @private
   */
  private applyPagination<T>(
    items: T[],
    pagination?: PaginationOptions
  ): { data: T[]; pagination: PaginationMeta } {
    const page = pagination?.page || 1;
    const limit = Math.min(pagination?.limit || 20, 100); // Cap at 100
    const offset = (page - 1) * limit;

    const paginatedItems = items.slice(offset, offset + limit);
    const total = items.length;
    const totalPages = Math.ceil(total / limit);

    return {
      data: paginatedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        count: paginatedItems.length,
        offset
      }
    };
  }

  /**
   * Determine device status based on last seen time
   * @private
   */
  private determineDeviceStatus(device: Device): DeviceStatus {
    if (device.deletedAt) {
      return DeviceStatus.Deleted;
    }

    const now = new Date();
    const lastSeen = new Date(device.lastSeen);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    if (lastSeen > oneHourAgo) {
      return DeviceStatus.Online;
    } else if (lastSeen > oneDayAgo) {
      return DeviceStatus.RecentlySeen;
    } else {
      return DeviceStatus.Offline;
    }
  }

  /**
   * Check if device is currently connected
   * @private
   */
  private isDeviceConnected(device: Device): boolean {
    const now = new Date();
    const lastSeen = new Date(device.lastSeen);
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    return lastSeen > fiveMinutesAgo && !device.deletedAt;
  }
}

export default new DeviceService();
