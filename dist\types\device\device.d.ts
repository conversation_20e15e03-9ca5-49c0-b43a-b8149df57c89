import { SoftDeletableEntity, BaseFilter } from '../base/common';
export declare enum DeviceStatus {
    Online = "online",
    Offline = "offline",
    RecentlySeen = "recently_seen",
    Deleted = "deleted",
    Unknown = "unknown"
}
export interface DeviceConnectionInfo {
    isConnected: boolean;
    lastSeen: Date;
    signalStrength?: number;
    batteryLevel?: number;
    networkType?: string;
    ipAddress?: string;
}
export interface Device extends SoftDeletableEntity {
    name: string;
    lastSeen: string;
    connection?: DeviceConnectionInfo;
    status?: DeviceStatus;
    capabilities?: DeviceCapabilities;
    stats?: DeviceStats;
    metadata?: Record<string, any>;
}
export interface DeviceCapabilities {
    canSendSms: boolean;
    canReceiveSms: boolean;
    supportsDeliveryReports: boolean;
    maxMessageLength: number;
    simSlots: number[];
    features: string[];
}
export interface DeviceStats {
    messagesSent: number;
    messagesReceived: number;
    messagesSentToday: number;
    successRate: number;
    avgResponseTime: number;
    lastActivity: Date;
}
export interface CreateDeviceData {
    name: string;
    lastSeen?: string;
    capabilities?: Partial<DeviceCapabilities>;
    metadata?: Record<string, any>;
}
export interface UpdateDeviceData {
    name?: string;
    lastSeen?: string;
    deletedAt?: string | null;
    connection?: Partial<DeviceConnectionInfo>;
    capabilities?: Partial<DeviceCapabilities>;
    metadata?: Record<string, any>;
}
export interface DeviceFilter extends BaseFilter {
    name?: string;
    active?: boolean;
    status?: DeviceStatus | DeviceStatus[];
    lastSeenAfter?: Date;
    lastSeenBefore?: Date;
    isConnected?: boolean;
    hasCapability?: string;
    hasSimSlot?: number;
}
export interface DeviceListItem {
    id: string;
    name: string;
    status: DeviceStatus;
    lastSeen: string;
    isConnected: boolean;
    batteryLevel?: number;
    messagesSentToday: number;
    successRate: number;
}
export interface DeviceDetails extends Device {
    recentActivity: DeviceActivity[];
    performance: DevicePerformanceMetrics;
}
export interface DeviceActivity {
    id: string;
    type: 'message_sent' | 'message_received' | 'connection_change' | 'error' | 'status_update';
    description: string;
    timestamp: Date;
    messageId?: string;
    data?: Record<string, any>;
}
export interface DevicePerformanceMetrics {
    messagesPerHour: number;
    avgDeliveryTime: number;
    errorRate: number;
    uptime: number;
    trend: 'improving' | 'stable' | 'declining';
}
//# sourceMappingURL=device.d.ts.map