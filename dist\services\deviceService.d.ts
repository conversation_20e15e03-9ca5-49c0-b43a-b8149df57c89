import { DeviceSettings, DeviceFilter, DeviceListItem, DeviceDetails, UpdateSettingsRequest, ApiResponse, PaginatedResponse, PaginationOptions, OperationResult, IDeviceService, ServiceContext } from '../types';
declare class DeviceService implements IDeviceService {
    private readonly logger;
    private readonly requestId;
    constructor(context?: Partial<ServiceContext>);
    getDevices(filter?: DeviceFilter, pagination?: PaginationOptions): Promise<PaginatedResponse<DeviceListItem>>;
    getDevice(deviceId: string): Promise<ApiResponse<DeviceDetails>>;
    deleteDevice(deviceId: string): Promise<OperationResult>;
    getSettings(deviceId?: string): Promise<ApiResponse<DeviceSettings>>;
    updateSettings(settings: DeviceSettings, deviceId?: string): Promise<OperationResult>;
    patchSettings(settings: UpdateSettingsRequest, deviceId?: string): Promise<OperationResult>;
    getHealth(): Promise<any>;
    getLogs(from?: Date, to?: Date): Promise<ApiResponse<any[]>>;
    exportInbox(deviceId: string, since: Date, until: Date): Promise<OperationResult>;
    getDeviceStats(deviceId?: string): Promise<ApiResponse<any>>;
    testConnection(): Promise<ApiResponse<{
        connected: boolean;
        timestamp: string;
    }>>;
    private generateRequestId;
    private applyDeviceFilters;
    private convertToDeviceListItems;
    private convertToDeviceDetails;
    private applyPagination;
    private determineDeviceStatus;
    private isDeviceConnected;
}
declare const _default: DeviceService;
export default _default;
//# sourceMappingURL=deviceService.d.ts.map