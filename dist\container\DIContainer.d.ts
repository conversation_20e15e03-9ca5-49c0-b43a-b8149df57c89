export declare enum ServiceLifetime {
    Singleton = "singleton",
    Transient = "transient",
    Scoped = "scoped"
}
export interface ServiceDescriptor<T = any> {
    name: string;
    factory: (container: DIContainer) => T;
    lifetime: ServiceLifetime;
    dependencies?: string[];
}
export declare class DIContainer {
    private static instance;
    private services;
    private singletons;
    private scoped;
    private constructor();
    static getInstance(): DIContainer;
    private registerCoreServices;
    register<T>(name: string, factory: (container: DIContainer) => T, lifetime?: ServiceLifetime, dependencies?: string[]): void;
    registerSingleton<T>(name: string, factory: (container: DIContainer) => T, dependencies?: string[]): void;
    registerTransient<T>(name: string, factory: (container: DIContainer) => T, dependencies?: string[]): void;
    registerScoped<T>(name: string, factory: (container: DIContainer) => T, dependencies?: string[]): void;
    resolve<T>(name: string): T;
    private resolveSingleton;
    private resolveScoped;
    private resolveTransient;
    private checkCircularDependencies;
    isRegistered(name: string): boolean;
    getRegisteredServices(): string[];
    clearScoped(): void;
    dispose(): Promise<void>;
    createScope(): DIContainer;
    initializeServices(): Promise<void>;
    healthCheck(): Promise<{
        healthy: boolean;
        services: Record<string, boolean>;
    }>;
}
export declare const container: DIContainer;
export declare function registerService<T>(name: string, factory: (container: DIContainer) => T, lifetime?: ServiceLifetime): void;
export declare function registerSingleton<T>(name: string, factory: (container: DIContainer) => T): void;
export declare function resolveService<T>(name: string): T;
//# sourceMappingURL=DIContainer.d.ts.map