export interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface SoftDeletableEntity extends BaseEntity {
    deletedAt?: Date | null;
}
export interface PaginationOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    count: number;
    offset: number;
}
export declare enum ProcessState {
    Pending = "pending",
    Processing = "processing",
    Queued = "queued",
    Sent = "sent",
    Delivered = "delivered",
    Failed = "failed",
    Cancelled = "cancelled",
    Expired = "expired",
    Retrying = "retrying"
}
export interface BaseFilter {
    dateFrom?: Date;
    dateTo?: Date;
    search?: string;
    includeDeleted?: boolean;
    status?: string | string[];
    createdAfter?: Date;
    createdBefore?: Date;
}
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type OptionalNullable<T> = T | null | undefined;
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};
export interface DateRange {
    from: Date;
    to: Date;
}
export interface StatusInfo {
    status: string;
    message: string;
    updatedAt: Date;
    metadata?: Record<string, any>;
}
export type DatabaseRow = Record<string, any>;
export interface TimestampFields {
    createdAt: Date;
    updatedAt: Date;
}
export interface OptionalTimestampFields {
    createdAt?: Date;
    updatedAt?: Date;
}
export interface OperationResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    metadata?: Record<string, any>;
}
export interface ServiceConfig {
    name: string;
    version: string;
    enabled: boolean;
    options?: Record<string, any>;
}
//# sourceMappingURL=common.d.ts.map