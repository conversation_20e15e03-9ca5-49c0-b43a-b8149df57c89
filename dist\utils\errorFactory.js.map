{"version": 3, "file": "errorFactory.js", "sourceRoot": "", "sources": ["../../src/utils/errorFactory.ts"], "names": [], "mappings": ";;;AAKA,oCAUkB;AAKlB,MAAa,YAAY;IAIvB,UAAU,CAAC,OAAe,EAAE,WAAyB,EAAE,OAA6B;QAClF,OAAO;YACL,IAAI,EAAE,iBAAiB;YACvB,OAAO;YACP,WAAW,EAAE,mEAAmE;YAChF,QAAQ,EAAE,qBAAa,CAAC,UAAU;YAClC,QAAQ,EAAE,qBAAa,CAAC,MAAM;YAC9B,UAAU,EAAE,GAAG;YACf,WAAW;YACX,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,cAAc,CAAC,OAAe,EAAE,OAA6B;QAC3D,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,OAAO;YACP,WAAW,EAAE,+EAA+E;YAC5F,QAAQ,EAAE,qBAAa,CAAC,cAAc;YACtC,QAAQ,EAAE,qBAAa,CAAC,MAAM;YAC9B,UAAU,EAAE,GAAG;YACf,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,aAAa,CAAC,OAAe,EAAE,OAA6B;QAC1D,OAAO;YACL,IAAI,EAAE,gBAAgB;YACtB,OAAO;YACP,WAAW,EAAE,qDAAqD;YAClE,QAAQ,EAAE,qBAAa,CAAC,aAAa;YACrC,QAAQ,EAAE,qBAAa,CAAC,MAAM;YAC9B,UAAU,EAAE,GAAG;YACf,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,QAAQ,CAAC,QAAgB,EAAE,UAAmB;QAC5C,MAAM,OAAO,GAAG,UAAU;YACxB,CAAC,CAAC,GAAG,QAAQ,qBAAqB,UAAU,aAAa;YACzD,CAAC,CAAC,GAAG,QAAQ,YAAY,CAAC;QAE5B,OAAO;YACL,IAAI,EAAE,gBAAgB;YACtB,OAAO;YACP,WAAW,EAAE,iBAAiB,QAAQ,CAAC,WAAW,EAAE,iFAAiF;YACrI,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,GAAG;YAC3B,UAAU,EAAE,GAAG;YACf,OAAO,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,QAAQ,CAAC,OAAe,EAAE,OAA6B;QACrD,OAAO;YACL,IAAI,EAAE,eAAe;YACrB,OAAO;YACP,WAAW,EAAE,6EAA6E;YAC1F,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,MAAM;YAC9B,UAAU,EAAE,GAAG;YACf,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,SAAS,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB;QAC1D,OAAO;YACL,IAAI,EAAE,iBAAiB;YACvB,OAAO,EAAE,wBAAwB,KAAK,iBAAiB,MAAM,EAAE;YAC/D,WAAW,EAAE,gFAAgF;YAC7F,QAAQ,EAAE,qBAAa,CAAC,SAAS;YACjC,QAAQ,EAAE,qBAAa,CAAC,MAAM;YAC9B,UAAU,EAAE,GAAG;YACf,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE;YACtC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,eAAe,CACb,WAAmB,EACnB,OAAe,EACf,OAMC;QAED,OAAO;YACL,IAAI,EAAE,uBAAuB;YAC7B,OAAO;YACP,WAAW,EAAE,wBAAwB,WAAW,2BAA2B;YAC3E,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,IAAI;YAC5B,UAAU,EAAE,GAAG;YACf,WAAW;YACX,QAAQ,EAAE,OAAO,EAAE,QAAQ;YAC3B,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,eAAe,EAAE,OAAO,EAAE,eAAe;YACzC,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;YACrC,UAAU,EAAE,OAAO,EAAE,UAAU;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,OAAO,CACL,SAAmD,EACnD,OAAe,EACf,OAIC;QAED,MAAM,iBAAiB,GAAG;YACxB,OAAO,EAAE,gCAAgC;YACzC,IAAI,EAAE,qBAAqB;YAC3B,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE,mBAAmB;SAC7B,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,OAAO;YACP,WAAW,EAAE,GAAG,iBAAiB,CAAC,SAAS,CAAC,wDAAwD;YACpG,QAAQ,EAAE,qBAAa,CAAC,OAAO;YAC/B,QAAQ,EAAE,qBAAa,CAAC,IAAI;YAC5B,UAAU,EAAE,GAAG;YACf,SAAS;YACT,IAAI,EAAE,OAAO,EAAE,IAAI;YACnB,IAAI,EAAE,OAAO,EAAE,IAAI;YACnB,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;YACrC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,aAAa,CACX,SAAiB,EACjB,YAAiB,EACjB,cAAsB,EACtB,UAAkB;QAElB,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,8BAA8B,SAAS,eAAe,cAAc,SAAS,OAAO,YAAY,EAAE;YAC3G,WAAW,EAAE,oEAAoE;YACjF,QAAQ,EAAE,qBAAa,CAAC,aAAa;YACrC,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,UAAU,EAAE,GAAG;YACf,SAAS;YACT,YAAY;YACZ,cAAc;YACd,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAKD,QAAQ,CAAC,OAAe,EAAE,OAA6B;QACrD,OAAO;YACL,IAAI,EAAE,eAAe;YACrB,OAAO;YACP,WAAW,EAAE,qGAAqG;YAClH,QAAQ,EAAE,qBAAa,CAAC,QAAQ;YAChC,QAAQ,EAAE,qBAAa,CAAC,IAAI;YAC5B,UAAU,EAAE,GAAG;YACf,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;CACF;AA9MD,oCA8MC;AAGY,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAKxC,MAAM,yBAAyB,GAAG,CAAC,QAAgB,EAAE,EAAE,CAC5D,oBAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAD/B,QAAA,yBAAyB,6BACM;AAErC,MAAM,6BAA6B,GAAG,CAAC,WAAmB,EAAE,EAAE,CACnE,oBAAY,CAAC,UAAU,CAAC,6BAA6B,EAAE,CAAC;QACtD,KAAK,EAAE,aAAa;QACpB,KAAK,EAAE,WAAW;QAClB,OAAO,EAAE,kEAAkE;QAC3E,IAAI,EAAE,sBAAsB;KAC7B,CAAC,CAAC,CAAC;AANO,QAAA,6BAA6B,iCAMpC;AAEC,MAAM,yBAAyB,GAAG,CAAC,MAAc,EAAE,SAAiB,EAAE,EAAE,CAC7E,oBAAY,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;QAC3C,KAAK,EAAE,SAAS;QAChB,KAAK,EAAE,MAAM;QACb,OAAO,EAAE,mBAAmB,MAAM,qCAAqC,SAAS,GAAG;QACnF,IAAI,EAAE,YAAY;KACnB,CAAC,CAAC,CAAC;AANO,QAAA,yBAAyB,6BAMhC;AAEC,MAAM,wBAAwB,GAAG,CAAC,QAAgB,EAAE,EAAE,CAC3D,oBAAY,CAAC,eAAe,CAAC,aAAa,EAAE,UAAU,QAAQ,aAAa,EAAE;IAC3E,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,EAAE;CACf,CAAC,CAAC;AAJQ,QAAA,wBAAwB,4BAIhC;AAEE,MAAM,4BAA4B,GAAG,CAAC,KAAa,EAAE,aAAqB,EAAE,EAAE,CACnF,oBAAY,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,aAAa,UAAU,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC;AADnE,QAAA,4BAA4B,gCACuC;AAEzE,MAAM,yBAAyB,GAAG,CAAC,IAAa,EAAE,EAAE,CACzD,oBAAY,CAAC,OAAO,CAAC,SAAS,EAAE,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AADrE,QAAA,yBAAyB,6BAC4C;AAE3E,MAAM,+BAA+B,GAAG,CAAC,SAAiB,EAAE,EAAE,CACnE,oBAAY,CAAC,aAAa,CACxB,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,WAAW,SAAS,+CAA+C,CACpE,CAAC;AANS,QAAA,+BAA+B,mCAMxC"}